import Customer from '../models/Customer.js';
import { validationResult } from 'express-validator';

class CustomerController {
    // الحصول على جميع العملاء
    static async getAll(req, res) {
        try {
            const { search, has_balance } = req.query;
            
            const filters = {};
            if (search) filters.search = search;
            if (has_balance === 'true') filters.has_balance = true;

            const customers = await Customer.getAll(filters);
            
            res.json({
                success: true,
                customers,
                count: customers.length
            });

        } catch (error) {
            console.error('خطأ في الحصول على العملاء:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على العملاء'
            });
        }
    }

    // الحصول على عميل بالمعرف
    static async getById(req, res) {
        try {
            const { id } = req.params;
            const customer = await Customer.findById(parseInt(id));

            if (!customer) {
                return res.status(404).json({
                    error: 'العميل غير موجود'
                });
            }

            res.json({
                success: true,
                customer
            });

        } catch (error) {
            console.error('خطأ في الحصول على العميل:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على العميل'
            });
        }
    }

    // البحث عن عميل بالهاتف
    static async getByPhone(req, res) {
        try {
            const { phone } = req.params;
            const customer = await Customer.findByPhone(phone);

            if (!customer) {
                return res.status(404).json({
                    error: 'العميل غير موجود'
                });
            }

            res.json({
                success: true,
                customer
            });

        } catch (error) {
            console.error('خطأ في البحث بالهاتف:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء البحث عن العميل'
            });
        }
    }

    // إنشاء عميل جديد
    static async create(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: 'بيانات غير صحيحة',
                    details: errors.array()
                });
            }

            const customerData = req.body;
            const newCustomer = await Customer.create(customerData);

            res.status(201).json({
                success: true,
                message: 'تم إنشاء العميل بنجاح',
                customer: newCustomer
            });

        } catch (error) {
            console.error('خطأ في إنشاء العميل:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء إنشاء العميل'
            });
        }
    }

    // تحديث عميل
    static async update(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: 'بيانات غير صحيحة',
                    details: errors.array()
                });
            }

            const { id } = req.params;
            const customerData = req.body;

            const result = await Customer.update(parseInt(id), customerData);

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'العميل غير موجود'
                });
            }

            res.json({
                success: true,
                message: 'تم تحديث العميل بنجاح'
            });

        } catch (error) {
            console.error('خطأ في تحديث العميل:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء تحديث العميل'
            });
        }
    }

    // تحديث رصيد العميل
    static async updateBalance(req, res) {
        try {
            const { id } = req.params;
            const { amount, operation = 'set' } = req.body;

            if (!amount || isNaN(amount)) {
                return res.status(400).json({
                    error: 'المبلغ يجب أن يكون رقم صحيح'
                });
            }

            const result = await Customer.updateBalance(parseInt(id), parseFloat(amount), operation);

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'العميل غير موجود'
                });
            }

            res.json({
                success: true,
                message: 'تم تحديث الرصيد بنجاح'
            });

        } catch (error) {
            console.error('خطأ في تحديث الرصيد:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء تحديث الرصيد'
            });
        }
    }

    // الحصول على العملاء المدينين
    static async getDebtors(req, res) {
        try {
            const customers = await Customer.getDebtors();
            
            res.json({
                success: true,
                customers,
                count: customers.length,
                total_debt: customers.reduce((sum, customer) => sum + Math.abs(customer.balance), 0)
            });

        } catch (error) {
            console.error('خطأ في الحصول على العملاء المدينين:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على العملاء'
            });
        }
    }

    // حذف عميل
    static async delete(req, res) {
        try {
            const { id } = req.params;
            const result = await Customer.delete(parseInt(id));

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'العميل غير موجود'
                });
            }

            res.json({
                success: true,
                message: 'تم حذف العميل بنجاح'
            });

        } catch (error) {
            console.error('خطأ في حذف العميل:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء حذف العميل'
            });
        }
    }
}

export default CustomerController;
