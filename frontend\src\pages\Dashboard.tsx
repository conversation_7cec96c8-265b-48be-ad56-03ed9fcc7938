import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { useAuthStore } from '../stores/authStore';
import { 
  ShoppingCart, 
  Package, 
  Users, 
  BarChart3, 
  Settings, 
  LogOut,
  DollarSign,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    {
      title: 'نقطة البيع',
      description: 'إجراء عمليات البيع والكاشير',
      icon: ShoppingCart,
      path: '/pos',
      color: 'bg-blue-500',
      roles: ['admin', 'manager', 'cashier']
    },
    {
      title: 'إدارة المنتجات',
      description: 'إضافة وتعديل المنتجات والمخزون',
      icon: Package,
      path: '/products',
      color: 'bg-green-500',
      roles: ['admin', 'manager']
    },
    {
      title: 'إدارة العملاء',
      description: 'إدارة بيانات العملاء والحسابات',
      icon: Users,
      path: '/customers',
      color: 'bg-purple-500',
      roles: ['admin', 'manager', 'cashier']
    },
    {
      title: 'التقارير',
      description: 'تقارير المبيعات والأرباح',
      icon: BarChart3,
      path: '/reports',
      color: 'bg-orange-500',
      roles: ['admin', 'manager']
    },
    {
      title: 'الإعدادات',
      description: 'إعدادات النظام والمستخدمين',
      icon: Settings,
      path: '/settings',
      color: 'bg-gray-500',
      roles: ['admin']
    }
  ];

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role || 'cashier')
  );

  const stats = [
    {
      title: 'مبيعات اليوم',
      value: '2,450 ر.س',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'عدد الفواتير',
      value: '24',
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'نمو المبيعات',
      value: '+12%',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'تنبيهات المخزون',
      value: '3',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* الهيدر */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <ShoppingCart className="h-8 w-8 text-primary ml-3" />
              <h1 className="text-xl font-bold text-gray-900">SmartPOS</h1>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-sm text-gray-700">
                مرحباً، <span className="font-medium">{user?.full_name}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center"
              >
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* المحتوى الرئيسي */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* القائمة الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMenuItems.map((item, index) => (
            <Card 
              key={index} 
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => navigate(item.path)}
            >
              <CardHeader>
                <div className="flex items-center">
                  <div className={`p-3 rounded-lg ${item.color} text-white`}>
                    <item.icon className="h-6 w-6" />
                  </div>
                  <div className="mr-4">
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  {item.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* الوصول السريع */}
        <div className="mt-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">الوصول السريع</h2>
          <div className="flex flex-wrap gap-4">
            <Button 
              onClick={() => navigate('/pos')}
              className="flex items-center"
            >
              <ShoppingCart className="h-4 w-4 ml-2" />
              بدء عملية بيع جديدة
            </Button>
            {(user?.role === 'admin' || user?.role === 'manager') && (
              <Button 
                variant="outline"
                onClick={() => navigate('/products/add')}
                className="flex items-center"
              >
                <Package className="h-4 w-4 ml-2" />
                إضافة منتج جديد
              </Button>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
