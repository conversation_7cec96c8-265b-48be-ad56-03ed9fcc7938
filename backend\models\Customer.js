import db from '../config/database.js';

class Customer {
    // إنشاء عميل جديد
    static async create(customerData) {
        const {
            name,
            phone,
            email,
            address,
            balance = 0
        } = customerData;

        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO customers (name, phone, email, address, balance)
                VALUES (?, ?, ?, ?, ?)
            `;

            db.run(sql, [name, phone, email, address, balance], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        id: this.lastID,
                        ...customerData,
                        created_at: new Date().toISOString()
                    });
                }
            });
        });
    }

    // الحصول على جميع العملاء
    static async getAll(filters = {}) {
        let sql = `
            SELECT * FROM customers
            WHERE is_active = 1
        `;
        const params = [];

        // تطبيق الفلاتر
        if (filters.search) {
            sql += ' AND (name LIKE ? OR phone LIKE ?)';
            params.push(`%${filters.search}%`, `%${filters.search}%`);
        }

        if (filters.has_balance) {
            sql += ' AND balance != 0';
        }

        sql += ' ORDER BY name ASC';

        return new Promise((resolve, reject) => {
            db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // البحث عن عميل بالمعرف
    static async findById(id) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM customers
                WHERE id = ? AND is_active = 1
            `;

            db.get(sql, [id], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // البحث عن عميل بالهاتف
    static async findByPhone(phone) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM customers
                WHERE phone = ? AND is_active = 1
            `;

            db.get(sql, [phone], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // تحديث بيانات العميل
    static async update(id, customerData) {
        const { name, phone, email, address } = customerData;

        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE customers 
                SET name = ?, phone = ?, email = ?, address = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;

            db.run(sql, [name, phone, email, address, id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // تحديث رصيد العميل
    static async updateBalance(id, amount, operation = 'set') {
        return new Promise((resolve, reject) => {
            let sql;
            if (operation === 'add') {
                sql = `
                    UPDATE customers 
                    SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `;
            } else if (operation === 'subtract') {
                sql = `
                    UPDATE customers 
                    SET balance = balance - ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `;
            } else {
                sql = `
                    UPDATE customers 
                    SET balance = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                `;
            }

            db.run(sql, [amount, id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }

    // الحصول على العملاء المدينين
    static async getDebtors() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM customers
                WHERE balance < 0 AND is_active = 1
                ORDER BY balance ASC
            `;

            db.all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // حذف عميل (إلغاء تفعيل)
    static async delete(id) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE customers 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;

            db.run(sql, [id], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ changes: this.changes });
                }
            });
        });
    }
}

export default Customer;
