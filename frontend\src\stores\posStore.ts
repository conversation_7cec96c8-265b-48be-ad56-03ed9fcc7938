import { create } from 'zustand';

interface Product {
  id: number;
  name: string;
  barcode?: string;
  selling_price: number;
  stock_quantity: number;
  unit: string;
  category_name?: string;
}

interface CartItem {
  product: Product;
  quantity: number;
  price: number;
  total: number;
}

interface Customer {
  id: number;
  name: string;
  phone?: string;
  balance: number;
}

interface POSState {
  cart: CartItem[];
  customer: Customer | null;
  discount: number;
  discountType: 'percentage' | 'fixed';
  paymentMethod: 'cash' | 'card' | 'transfer' | 'mixed';
  isLoading: boolean;
  error: string | null;
}

interface POSActions {
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: number) => void;
  updateQuantity: (productId: number, quantity: number) => void;
  clearCart: () => void;
  setCustomer: (customer: Customer | null) => void;
  setDiscount: (discount: number, type: 'percentage' | 'fixed') => void;
  setPaymentMethod: (method: 'cash' | 'card' | 'transfer' | 'mixed') => void;
  getSubtotal: () => number;
  getDiscountAmount: () => number;
  getTotal: () => number;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const usePOSStore = create<POSState & POSActions>((set, get) => ({
  // الحالة الأولية
  cart: [],
  customer: null,
  discount: 0,
  discountType: 'percentage',
  paymentMethod: 'cash',
  isLoading: false,
  error: null,

  // الإجراءات
  addToCart: (product: Product, quantity = 1) => {
    const { cart } = get();
    const existingItem = cart.find(item => item.product.id === product.id);

    if (existingItem) {
      // تحديث الكمية إذا كان المنتج موجود
      set({
        cart: cart.map(item =>
          item.product.id === product.id
            ? {
                ...item,
                quantity: item.quantity + quantity,
                total: (item.quantity + quantity) * item.price
              }
            : item
        )
      });
    } else {
      // إضافة منتج جديد
      const newItem: CartItem = {
        product,
        quantity,
        price: product.selling_price,
        total: product.selling_price * quantity
      };
      set({ cart: [...cart, newItem] });
    }
  },

  removeFromCart: (productId: number) => {
    const { cart } = get();
    set({ cart: cart.filter(item => item.product.id !== productId) });
  },

  updateQuantity: (productId: number, quantity: number) => {
    if (quantity <= 0) {
      get().removeFromCart(productId);
      return;
    }

    const { cart } = get();
    set({
      cart: cart.map(item =>
        item.product.id === productId
          ? {
              ...item,
              quantity,
              total: quantity * item.price
            }
          : item
      )
    });
  },

  clearCart: () => {
    set({ 
      cart: [], 
      customer: null, 
      discount: 0, 
      discountType: 'percentage',
      paymentMethod: 'cash'
    });
  },

  setCustomer: (customer: Customer | null) => {
    set({ customer });
  },

  setDiscount: (discount: number, type: 'percentage' | 'fixed') => {
    set({ discount, discountType: type });
  },

  setPaymentMethod: (method: 'cash' | 'card' | 'transfer' | 'mixed') => {
    set({ paymentMethod: method });
  },

  getSubtotal: () => {
    const { cart } = get();
    return cart.reduce((total, item) => total + item.total, 0);
  },

  getDiscountAmount: () => {
    const { discount, discountType } = get();
    const subtotal = get().getSubtotal();
    
    if (discountType === 'percentage') {
      return (subtotal * discount) / 100;
    } else {
      return Math.min(discount, subtotal);
    }
  },

  getTotal: () => {
    const subtotal = get().getSubtotal();
    const discountAmount = get().getDiscountAmount();
    return Math.max(0, subtotal - discountAmount);
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },
}));
