import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { usePOSStore } from '../stores/posStore';
import { useAuthStore } from '../stores/authStore';
import { productsAPI } from '../services/api';
import { 
  ShoppingCart, 
  Search, 
  Plus, 
  Minus, 
  Trash2, 
  User, 
  CreditCard,
  ArrowLeft,
  Scan
} from 'lucide-react';

interface Product {
  id: number;
  name: string;
  barcode?: string;
  selling_price: number;
  stock_quantity: number;
  unit: string;
  category_name?: string;
}

const POS: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [barcodeInput, setBarcodeInput] = useState('');

  const { user } = useAuthStore();
  const {
    cart,
    customer,
    discount,
    paymentMethod,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getSubtotal,
    getDiscountAmount,
    getTotal
  } = usePOSStore();

  const navigate = useNavigate();

  // تحميل المنتجات
  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const response = await productsAPI.getAll();
      setProducts(response.products || []);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
    }
  };

  // البحث في المنتجات
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode?.includes(searchTerm)
  );

  // البحث بالباركود
  const handleBarcodeSearch = async () => {
    if (!barcodeInput.trim()) return;

    try {
      const product = await productsAPI.getByBarcode(barcodeInput);
      if (product) {
        addToCart(product);
        setBarcodeInput('');
      }
    } catch (error) {
      console.error('المنتج غير موجود');
    }
  };

  // إتمام عملية البيع
  const handleCheckout = () => {
    if (cart.length === 0) return;
    
    // هنا يمكن إضافة منطق إتمام البيع
    console.log('إتمام البيع:', {
      cart,
      customer,
      subtotal: getSubtotal(),
      discount: getDiscountAmount(),
      total: getTotal(),
      paymentMethod
    });
    
    // مسح السلة بعد إتمام البيع
    clearCart();
    alert('تم إتمام عملية البيع بنجاح!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* الهيدر */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                onClick={() => navigate('/dashboard')}
                className="ml-4"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <ShoppingCart className="h-6 w-6 text-primary ml-3" />
              <h1 className="text-xl font-bold text-gray-900">نقطة البيع</h1>
            </div>
            
            <div className="text-sm text-gray-700">
              الكاشير: <span className="font-medium">{user?.full_name}</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* قسم المنتجات */}
          <div className="lg:col-span-2">
            {/* البحث والباركود */}
            <div className="mb-6 space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="مسح الباركود أو إدخاله يدوياً"
                    value={barcodeInput}
                    onChange={(e) => setBarcodeInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleBarcodeSearch()}
                  />
                </div>
                <Button onClick={handleBarcodeSearch}>
                  <Scan className="h-4 w-4 ml-2" />
                  مسح
                </Button>
              </div>
            </div>

            {/* شبكة المنتجات */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredProducts.map((product) => (
                <Card 
                  key={product.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => addToCart(product)}
                >
                  <CardContent className="p-4">
                    <h3 className="font-medium text-sm mb-2 line-clamp-2">
                      {product.name}
                    </h3>
                    <p className="text-lg font-bold text-primary mb-1">
                      {product.selling_price} ر.س
                    </p>
                    <p className="text-xs text-gray-500">
                      المخزون: {product.stock_quantity} {product.unit}
                    </p>
                    {product.category_name && (
                      <p className="text-xs text-gray-400 mt-1">
                        {product.category_name}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* سلة التسوق */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ShoppingCart className="h-5 w-5 ml-2" />
                  سلة التسوق ({cart.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* العميل */}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <User className="h-4 w-4 ml-2" />
                    <span className="text-sm">
                      {customer ? customer.name : 'عميل عادي'}
                    </span>
                  </div>
                  <Button variant="outline" size="sm">
                    تغيير
                  </Button>
                </div>

                {/* عناصر السلة */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {cart.map((item) => (
                    <div key={item.product.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{item.product.name}</p>
                        <p className="text-xs text-gray-500">
                          {item.price} ر.س × {item.quantity}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="text-sm font-medium w-8 text-center">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => removeFromCart(item.product.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {cart.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <ShoppingCart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>السلة فارغة</p>
                  </div>
                )}

                {/* الملخص المالي */}
                {cart.length > 0 && (
                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between text-sm">
                      <span>المجموع الفرعي:</span>
                      <span>{getSubtotal().toFixed(2)} ر.س</span>
                    </div>
                    
                    {discount > 0 && (
                      <div className="flex justify-between text-sm text-red-600">
                        <span>الخصم:</span>
                        <span>-{getDiscountAmount().toFixed(2)} ر.س</span>
                      </div>
                    )}
                    
                    <div className="flex justify-between text-lg font-bold pt-2 border-t">
                      <span>الإجمالي:</span>
                      <span>{getTotal().toFixed(2)} ر.س</span>
                    </div>
                  </div>
                )}

                {/* أزرار العمليات */}
                <div className="space-y-2 pt-4">
                  <Button
                    className="w-full"
                    onClick={handleCheckout}
                    disabled={cart.length === 0}
                  >
                    <CreditCard className="h-4 w-4 ml-2" />
                    إتمام البيع
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm">
                      خصم
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={clearCart}
                      disabled={cart.length === 0}
                    >
                      مسح الكل
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default POS;
