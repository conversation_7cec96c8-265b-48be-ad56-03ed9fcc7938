import db from './config/database.js';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function seedDatabase() {
    try {
        console.log('🌱 بدء إدراج البيانات التجريبية...');
        
        const seedPath = join(__dirname, '../database/seeds.sql');
        const seedData = fs.readFileSync(seedPath, 'utf8');
        
        // تقسيم الاستعلامات
        const statements = seedData
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);

        // تنفيذ كل استعلام
        for (const statement of statements) {
            await new Promise((resolve, reject) => {
                db.run(statement, function(err) {
                    if (err) {
                        console.error('❌ خطأ في تنفيذ:', statement.substring(0, 50) + '...');
                        console.error('التفاصيل:', err.message);
                        reject(err);
                    } else {
                        console.log('✅ تم تنفيذ:', statement.substring(0, 50) + '...');
                        resolve(this);
                    }
                });
            });
        }

        console.log('🎉 تم إدراج جميع البيانات التجريبية بنجاح!');
        
        // عرض إحصائيات
        const stats = await getStats();
        console.log('\n📊 إحصائيات قاعدة البيانات:');
        console.log(`👥 المستخدمين: ${stats.users}`);
        console.log(`📦 المنتجات: ${stats.products}`);
        console.log(`🏷️ الفئات: ${stats.categories}`);
        console.log(`👤 العملاء: ${stats.customers}`);
        console.log(`🏪 الموردين: ${stats.suppliers}`);
        
    } catch (error) {
        console.error('❌ خطأ في إدراج البيانات:', error.message);
    } finally {
        db.close();
        process.exit(0);
    }
}

function getStats() {
    return new Promise((resolve) => {
        const stats = {};
        let completed = 0;
        const tables = ['users', 'products', 'categories', 'customers', 'suppliers'];
        
        tables.forEach(table => {
            db.get(`SELECT COUNT(*) as count FROM ${table}`, (err, row) => {
                if (!err) {
                    stats[table] = row.count;
                }
                completed++;
                if (completed === tables.length) {
                    resolve(stats);
                }
            });
        });
    });
}

// تشغيل السكريبت
seedDatabase();
