import axios from 'axios';

// إعداد الـ base URL للـ API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// إنشاء instance من axios
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة interceptor للـ request لإضافة التوكن
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('smartpos_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للـ response للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // إزالة التوكن المنتهي الصلاحية
      localStorage.removeItem('smartpos_token');
      localStorage.removeItem('smartpos_user');
      // إعادة توجيه لصفحة تسجيل الدخول
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// خدمات المصادقة
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  register: async (userData: {
    username: string;
    email?: string;
    password: string;
    full_name: string;
    role?: string;
  }) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (userData: { email?: string; full_name: string }) => {
    const response = await api.put('/auth/profile', userData);
    return response.data;
  },

  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
  }) => {
    const response = await api.put('/auth/change-password', passwordData);
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
};

// خدمات المنتجات
export const productsAPI = {
  getAll: async (filters?: {
    category_id?: number;
    search?: string;
    low_stock?: boolean;
  }) => {
    const response = await api.get('/products', { params: filters });
    return response.data;
  },

  getById: async (id: number) => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  getByBarcode: async (barcode: string) => {
    const response = await api.get(`/products/barcode/${barcode}`);
    return response.data;
  },

  create: async (productData: any) => {
    const response = await api.post('/products', productData);
    return response.data;
  },

  update: async (id: number, productData: any) => {
    const response = await api.put(`/products/${id}`, productData);
    return response.data;
  },

  delete: async (id: number) => {
    const response = await api.delete(`/products/${id}`);
    return response.data;
  },

  updateStock: async (id: number, quantity: number, operation: 'set' | 'add' | 'subtract') => {
    const response = await api.patch(`/products/${id}/stock`, { quantity, operation });
    return response.data;
  },

  getLowStock: async () => {
    const response = await api.get('/products/low-stock');
    return response.data;
  },
};

// خدمات العملاء
export const customersAPI = {
  getAll: async () => {
    const response = await api.get('/customers');
    return response.data;
  },

  getById: async (id: number) => {
    const response = await api.get(`/customers/${id}`);
    return response.data;
  },

  create: async (customerData: any) => {
    const response = await api.post('/customers', customerData);
    return response.data;
  },

  update: async (id: number, customerData: any) => {
    const response = await api.put(`/customers/${id}`, customerData);
    return response.data;
  },

  delete: async (id: number) => {
    const response = await api.delete(`/customers/${id}`);
    return response.data;
  },
};

// خدمات المبيعات
export const salesAPI = {
  getAll: async (filters?: {
    start_date?: string;
    end_date?: string;
    customer_id?: number;
  }) => {
    const response = await api.get('/sales', { params: filters });
    return response.data;
  },

  getById: async (id: number) => {
    const response = await api.get(`/sales/${id}`);
    return response.data;
  },

  create: async (saleData: any) => {
    const response = await api.post('/sales', saleData);
    return response.data;
  },

  getDailyReport: async (date?: string) => {
    const response = await api.get('/sales/reports/daily', { params: { date } });
    return response.data;
  },

  getMonthlyReport: async (month?: string, year?: string) => {
    const response = await api.get('/sales/reports/monthly', { params: { month, year } });
    return response.data;
  },
};

export default api;
