(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function c(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(o){if(o.ep)return;o.ep=!0;const f=c(o);fetch(o.href,f)}})();function sg(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var vc={exports:{}},ru={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fh;function cg(){if(Fh)return ru;Fh=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function c(s,o,f){var h=null;if(f!==void 0&&(h=""+f),o.key!==void 0&&(h=""+o.key),"key"in o){f={};for(var g in o)g!=="key"&&(f[g]=o[g])}else f=o;return o=f.ref,{$$typeof:a,type:s,key:h,ref:o!==void 0?o:null,props:f}}return ru.Fragment=i,ru.jsx=c,ru.jsxs=c,ru}var Wh;function og(){return Wh||(Wh=1,vc.exports=cg()),vc.exports}var E=og(),bc={exports:{}},se={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ph;function fg(){if(Ph)return se;Ph=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),A=Symbol.iterator;function O(S){return S===null||typeof S!="object"?null:(S=A&&S[A]||S["@@iterator"],typeof S=="function"?S:null)}var k={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,z={};function U(S,Y,F){this.props=S,this.context=Y,this.refs=z,this.updater=F||k}U.prototype.isReactComponent={},U.prototype.setState=function(S,Y){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,Y,"setState")},U.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function B(){}B.prototype=U.prototype;function G(S,Y,F){this.props=S,this.context=Y,this.refs=z,this.updater=F||k}var Q=G.prototype=new B;Q.constructor=G,R(Q,U.prototype),Q.isPureReactComponent=!0;var le=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},ce=Object.prototype.hasOwnProperty;function J(S,Y,F,X,W,me){return F=me.ref,{$$typeof:a,type:S,key:Y,ref:F!==void 0?F:null,props:me}}function $(S,Y){return J(S.type,Y,void 0,void 0,void 0,S.props)}function he(S){return typeof S=="object"&&S!==null&&S.$$typeof===a}function Qe(S){var Y={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(F){return Y[F]})}var ut=/\/+/g;function He(S,Y){return typeof S=="object"&&S!==null&&S.key!=null?Qe(""+S.key):Y.toString(36)}function Yt(){}function Ut(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(Yt,Yt):(S.status="pending",S.then(function(Y){S.status==="pending"&&(S.status="fulfilled",S.value=Y)},function(Y){S.status==="pending"&&(S.status="rejected",S.reason=Y)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Me(S,Y,F,X,W){var me=typeof S;(me==="undefined"||me==="boolean")&&(S=null);var ie=!1;if(S===null)ie=!0;else switch(me){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(S.$$typeof){case a:case i:ie=!0;break;case v:return ie=S._init,Me(ie(S._payload),Y,F,X,W)}}if(ie)return W=W(S),ie=X===""?"."+He(S,0):X,le(W)?(F="",ie!=null&&(F=ie.replace(ut,"$&/")+"/"),Me(W,Y,F,"",function(pt){return pt})):W!=null&&(he(W)&&(W=$(W,F+(W.key==null||S&&S.key===W.key?"":(""+W.key).replace(ut,"$&/")+"/")+ie)),Y.push(W)),1;ie=0;var ve=X===""?".":X+":";if(le(S))for(var Oe=0;Oe<S.length;Oe++)X=S[Oe],me=ve+He(X,Oe),ie+=Me(X,Y,F,me,W);else if(Oe=O(S),typeof Oe=="function")for(S=Oe.call(S),Oe=0;!(X=S.next()).done;)X=X.value,me=ve+He(X,Oe++),ie+=Me(X,Y,F,me,W);else if(me==="object"){if(typeof S.then=="function")return Me(Ut(S),Y,F,X,W);throw Y=String(S),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return ie}function j(S,Y,F){if(S==null)return S;var X=[],W=0;return Me(S,X,"","",function(me){return Y.call(F,me,W++)}),X}function Z(S){if(S._status===-1){var Y=S._result;Y=Y(),Y.then(function(F){(S._status===0||S._status===-1)&&(S._status=1,S._result=F)},function(F){(S._status===0||S._status===-1)&&(S._status=2,S._result=F)}),S._status===-1&&(S._status=0,S._result=Y)}if(S._status===1)return S._result.default;throw S._result}var V=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function xe(){}return se.Children={map:j,forEach:function(S,Y,F){j(S,function(){Y.apply(this,arguments)},F)},count:function(S){var Y=0;return j(S,function(){Y++}),Y},toArray:function(S){return j(S,function(Y){return Y})||[]},only:function(S){if(!he(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},se.Component=U,se.Fragment=c,se.Profiler=o,se.PureComponent=G,se.StrictMode=s,se.Suspense=y,se.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,se.__COMPILER_RUNTIME={__proto__:null,c:function(S){return K.H.useMemoCache(S)}},se.cache=function(S){return function(){return S.apply(null,arguments)}},se.cloneElement=function(S,Y,F){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var X=R({},S.props),W=S.key,me=void 0;if(Y!=null)for(ie in Y.ref!==void 0&&(me=void 0),Y.key!==void 0&&(W=""+Y.key),Y)!ce.call(Y,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&Y.ref===void 0||(X[ie]=Y[ie]);var ie=arguments.length-2;if(ie===1)X.children=F;else if(1<ie){for(var ve=Array(ie),Oe=0;Oe<ie;Oe++)ve[Oe]=arguments[Oe+2];X.children=ve}return J(S.type,W,void 0,void 0,me,X)},se.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:f,_context:S},S},se.createElement=function(S,Y,F){var X,W={},me=null;if(Y!=null)for(X in Y.key!==void 0&&(me=""+Y.key),Y)ce.call(Y,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(W[X]=Y[X]);var ie=arguments.length-2;if(ie===1)W.children=F;else if(1<ie){for(var ve=Array(ie),Oe=0;Oe<ie;Oe++)ve[Oe]=arguments[Oe+2];W.children=ve}if(S&&S.defaultProps)for(X in ie=S.defaultProps,ie)W[X]===void 0&&(W[X]=ie[X]);return J(S,me,void 0,void 0,null,W)},se.createRef=function(){return{current:null}},se.forwardRef=function(S){return{$$typeof:g,render:S}},se.isValidElement=he,se.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:Z}},se.memo=function(S,Y){return{$$typeof:m,type:S,compare:Y===void 0?null:Y}},se.startTransition=function(S){var Y=K.T,F={};K.T=F;try{var X=S(),W=K.S;W!==null&&W(F,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(xe,V)}catch(me){V(me)}finally{K.T=Y}},se.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},se.use=function(S){return K.H.use(S)},se.useActionState=function(S,Y,F){return K.H.useActionState(S,Y,F)},se.useCallback=function(S,Y){return K.H.useCallback(S,Y)},se.useContext=function(S){return K.H.useContext(S)},se.useDebugValue=function(){},se.useDeferredValue=function(S,Y){return K.H.useDeferredValue(S,Y)},se.useEffect=function(S,Y,F){var X=K.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(S,Y)},se.useId=function(){return K.H.useId()},se.useImperativeHandle=function(S,Y,F){return K.H.useImperativeHandle(S,Y,F)},se.useInsertionEffect=function(S,Y){return K.H.useInsertionEffect(S,Y)},se.useLayoutEffect=function(S,Y){return K.H.useLayoutEffect(S,Y)},se.useMemo=function(S,Y){return K.H.useMemo(S,Y)},se.useOptimistic=function(S,Y){return K.H.useOptimistic(S,Y)},se.useReducer=function(S,Y,F){return K.H.useReducer(S,Y,F)},se.useRef=function(S){return K.H.useRef(S)},se.useState=function(S){return K.H.useState(S)},se.useSyncExternalStore=function(S,Y,F){return K.H.useSyncExternalStore(S,Y,F)},se.useTransition=function(){return K.H.useTransition()},se.version="19.1.0",se}var Ih;function Vc(){return Ih||(Ih=1,bc.exports=fg()),bc.exports}var M=Vc();const em=sg(M);var Sc={exports:{}},su={},xc={exports:{}},Ec={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tm;function dg(){return tm||(tm=1,function(a){function i(j,Z){var V=j.length;j.push(Z);e:for(;0<V;){var xe=V-1>>>1,S=j[xe];if(0<o(S,Z))j[xe]=Z,j[V]=S,V=xe;else break e}}function c(j){return j.length===0?null:j[0]}function s(j){if(j.length===0)return null;var Z=j[0],V=j.pop();if(V!==Z){j[0]=V;e:for(var xe=0,S=j.length,Y=S>>>1;xe<Y;){var F=2*(xe+1)-1,X=j[F],W=F+1,me=j[W];if(0>o(X,V))W<S&&0>o(me,X)?(j[xe]=me,j[W]=V,xe=W):(j[xe]=X,j[F]=V,xe=F);else if(W<S&&0>o(me,V))j[xe]=me,j[W]=V,xe=W;else break e}}return Z}function o(j,Z){var V=j.sortIndex-Z.sortIndex;return V!==0?V:j.id-Z.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var h=Date,g=h.now();a.unstable_now=function(){return h.now()-g}}var y=[],m=[],v=1,A=null,O=3,k=!1,R=!1,z=!1,U=!1,B=typeof setTimeout=="function"?setTimeout:null,G=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function le(j){for(var Z=c(m);Z!==null;){if(Z.callback===null)s(m);else if(Z.startTime<=j)s(m),Z.sortIndex=Z.expirationTime,i(y,Z);else break;Z=c(m)}}function K(j){if(z=!1,le(j),!R)if(c(y)!==null)R=!0,ce||(ce=!0,He());else{var Z=c(m);Z!==null&&Me(K,Z.startTime-j)}}var ce=!1,J=-1,$=5,he=-1;function Qe(){return U?!0:!(a.unstable_now()-he<$)}function ut(){if(U=!1,ce){var j=a.unstable_now();he=j;var Z=!0;try{e:{R=!1,z&&(z=!1,G(J),J=-1),k=!0;var V=O;try{t:{for(le(j),A=c(y);A!==null&&!(A.expirationTime>j&&Qe());){var xe=A.callback;if(typeof xe=="function"){A.callback=null,O=A.priorityLevel;var S=xe(A.expirationTime<=j);if(j=a.unstable_now(),typeof S=="function"){A.callback=S,le(j),Z=!0;break t}A===c(y)&&s(y),le(j)}else s(y);A=c(y)}if(A!==null)Z=!0;else{var Y=c(m);Y!==null&&Me(K,Y.startTime-j),Z=!1}}break e}finally{A=null,O=V,k=!1}Z=void 0}}finally{Z?He():ce=!1}}}var He;if(typeof Q=="function")He=function(){Q(ut)};else if(typeof MessageChannel<"u"){var Yt=new MessageChannel,Ut=Yt.port2;Yt.port1.onmessage=ut,He=function(){Ut.postMessage(null)}}else He=function(){B(ut,0)};function Me(j,Z){J=B(function(){j(a.unstable_now())},Z)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(j){j.callback=null},a.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<j?Math.floor(1e3/j):5},a.unstable_getCurrentPriorityLevel=function(){return O},a.unstable_next=function(j){switch(O){case 1:case 2:case 3:var Z=3;break;default:Z=O}var V=O;O=Z;try{return j()}finally{O=V}},a.unstable_requestPaint=function(){U=!0},a.unstable_runWithPriority=function(j,Z){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var V=O;O=j;try{return Z()}finally{O=V}},a.unstable_scheduleCallback=function(j,Z,V){var xe=a.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?xe+V:xe):V=xe,j){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=V+S,j={id:v++,callback:Z,priorityLevel:j,startTime:V,expirationTime:S,sortIndex:-1},V>xe?(j.sortIndex=V,i(m,j),c(y)===null&&j===c(m)&&(z?(G(J),J=-1):z=!0,Me(K,V-xe))):(j.sortIndex=S,i(y,j),R||k||(R=!0,ce||(ce=!0,He()))),j},a.unstable_shouldYield=Qe,a.unstable_wrapCallback=function(j){var Z=O;return function(){var V=O;O=Z;try{return j.apply(this,arguments)}finally{O=V}}}}(Ec)),Ec}var lm;function hg(){return lm||(lm=1,xc.exports=dg()),xc.exports}var Rc={exports:{}},lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nm;function mg(){if(nm)return lt;nm=1;var a=Vc();function i(y){var m="https://react.dev/errors/"+y;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(i(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(y,m,v){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:A==null?null:""+A,children:y,containerInfo:m,implementation:v}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,m){if(y==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,lt.createPortal=function(y,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return f(y,m,null,v)},lt.flushSync=function(y){var m=h.T,v=s.p;try{if(h.T=null,s.p=2,y)return y()}finally{h.T=m,s.p=v,s.d.f()}},lt.preconnect=function(y,m){typeof y=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(y,m))},lt.prefetchDNS=function(y){typeof y=="string"&&s.d.D(y)},lt.preinit=function(y,m){if(typeof y=="string"&&m&&typeof m.as=="string"){var v=m.as,A=g(v,m.crossOrigin),O=typeof m.integrity=="string"?m.integrity:void 0,k=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?s.d.S(y,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:A,integrity:O,fetchPriority:k}):v==="script"&&s.d.X(y,{crossOrigin:A,integrity:O,fetchPriority:k,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},lt.preinitModule=function(y,m){if(typeof y=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=g(m.as,m.crossOrigin);s.d.M(y,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(y)},lt.preload=function(y,m){if(typeof y=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,A=g(v,m.crossOrigin);s.d.L(y,v,{crossOrigin:A,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},lt.preloadModule=function(y,m){if(typeof y=="string")if(m){var v=g(m.as,m.crossOrigin);s.d.m(y,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(y)},lt.requestFormReset=function(y){s.d.r(y)},lt.unstable_batchedUpdates=function(y,m){return y(m)},lt.useFormState=function(y,m,v){return h.H.useFormState(y,m,v)},lt.useFormStatus=function(){return h.H.useHostTransitionStatus()},lt.version="19.1.0",lt}var am;function pg(){if(am)return Rc.exports;am=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Rc.exports=mg(),Rc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var um;function yg(){if(um)return su;um=1;var a=hg(),i=Vc(),c=pg();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(f(e)!==e)throw Error(s(188))}function y(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var l=e,n=t;;){var u=l.return;if(u===null)break;var r=u.alternate;if(r===null){if(n=u.return,n!==null){l=n;continue}break}if(u.child===r.child){for(r=u.child;r;){if(r===l)return g(u),e;if(r===n)return g(u),t;r=r.sibling}throw Error(s(188))}if(l.return!==n.return)l=u,n=r;else{for(var d=!1,p=u.child;p;){if(p===l){d=!0,l=u,n=r;break}if(p===n){d=!0,n=u,l=r;break}p=p.sibling}if(!d){for(p=r.child;p;){if(p===l){d=!0,l=r,n=u;break}if(p===n){d=!0,n=r,l=u;break}p=p.sibling}if(!d)throw Error(s(189))}}if(l.alternate!==n)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,A=Symbol.for("react.element"),O=Symbol.for("react.transitional.element"),k=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),U=Symbol.for("react.profiler"),B=Symbol.for("react.provider"),G=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),le=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),ce=Symbol.for("react.suspense_list"),J=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),he=Symbol.for("react.activity"),Qe=Symbol.for("react.memo_cache_sentinel"),ut=Symbol.iterator;function He(e){return e===null||typeof e!="object"?null:(e=ut&&e[ut]||e["@@iterator"],typeof e=="function"?e:null)}var Yt=Symbol.for("react.client.reference");function Ut(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Yt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case U:return"Profiler";case z:return"StrictMode";case K:return"Suspense";case ce:return"SuspenseList";case he:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case k:return"Portal";case Q:return(e.displayName||"Context")+".Provider";case G:return(e._context.displayName||"Context")+".Consumer";case le:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case J:return t=e.displayName||null,t!==null?t:Ut(e.type)||"Memo";case $:t=e._payload,e=e._init;try{return Ut(e(t))}catch{}}return null}var Me=Array.isArray,j=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},xe=[],S=-1;function Y(e){return{current:e}}function F(e){0>S||(e.current=xe[S],xe[S]=null,S--)}function X(e,t){S++,xe[S]=e.current,e.current=t}var W=Y(null),me=Y(null),ie=Y(null),ve=Y(null);function Oe(e,t){switch(X(ie,t),X(me,e),X(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Th(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Th(t),e=Ah(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(W),X(W,e)}function pt(){F(W),F(me),F(ie)}function ml(e){e.memoizedState!==null&&X(ve,e);var t=W.current,l=Ah(t,e.type);t!==l&&(X(me,e),X(W,l))}function pl(e){me.current===e&&(F(W),F(me)),ve.current===e&&(F(ve),lu._currentValue=V)}var yl=Object.prototype.hasOwnProperty,ur=a.unstable_scheduleCallback,ir=a.unstable_cancelCallback,kp=a.unstable_shouldYield,Yp=a.unstable_requestPaint,Gt=a.unstable_now,Gp=a.unstable_getCurrentPriorityLevel,lo=a.unstable_ImmediatePriority,no=a.unstable_UserBlockingPriority,Eu=a.unstable_NormalPriority,Vp=a.unstable_LowPriority,ao=a.unstable_IdlePriority,Xp=a.log,Qp=a.unstable_setDisableYieldValue,oa=null,yt=null;function gl(e){if(typeof Xp=="function"&&Qp(e),yt&&typeof yt.setStrictMode=="function")try{yt.setStrictMode(oa,e)}catch{}}var gt=Math.clz32?Math.clz32:Jp,Zp=Math.log,Kp=Math.LN2;function Jp(e){return e>>>=0,e===0?32:31-(Zp(e)/Kp|0)|0}var Ru=256,Tu=4194304;function Ql(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Au(e,t,l){var n=e.pendingLanes;if(n===0)return 0;var u=0,r=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var p=n&134217727;return p!==0?(n=p&~r,n!==0?u=Ql(n):(d&=p,d!==0?u=Ql(d):l||(l=p&~e,l!==0&&(u=Ql(l))))):(p=n&~r,p!==0?u=Ql(p):d!==0?u=Ql(d):l||(l=n&~e,l!==0&&(u=Ql(l)))),u===0?0:t!==0&&t!==u&&(t&r)===0&&(r=u&-u,l=t&-t,r>=l||r===32&&(l&4194048)!==0)?t:u}function fa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function $p(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function uo(){var e=Ru;return Ru<<=1,(Ru&4194048)===0&&(Ru=256),e}function io(){var e=Tu;return Tu<<=1,(Tu&62914560)===0&&(Tu=4194304),e}function rr(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function da(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Fp(e,t,l,n,u,r){var d=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var p=e.entanglements,b=e.expirationTimes,N=e.hiddenUpdates;for(l=d&~l;0<l;){var H=31-gt(l),q=1<<H;p[H]=0,b[H]=-1;var _=N[H];if(_!==null)for(N[H]=null,H=0;H<_.length;H++){var C=_[H];C!==null&&(C.lane&=-536870913)}l&=~q}n!==0&&ro(e,n,0),r!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=r&~(d&~t))}function ro(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var n=31-gt(t);e.entangledLanes|=t,e.entanglements[n]=e.entanglements[n]|1073741824|l&4194090}function so(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var n=31-gt(l),u=1<<n;u&t|e[n]&t&&(e[n]|=t),l&=~u}}function sr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function cr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function co(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:Xh(e.type))}function Wp(e,t){var l=Z.p;try{return Z.p=e,t()}finally{Z.p=l}}var vl=Math.random().toString(36).slice(2),et="__reactFiber$"+vl,st="__reactProps$"+vl,mn="__reactContainer$"+vl,or="__reactEvents$"+vl,Pp="__reactListeners$"+vl,Ip="__reactHandles$"+vl,oo="__reactResources$"+vl,ha="__reactMarker$"+vl;function fr(e){delete e[et],delete e[st],delete e[or],delete e[Pp],delete e[Ip]}function pn(e){var t=e[et];if(t)return t;for(var l=e.parentNode;l;){if(t=l[mn]||l[et]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=_h(e);e!==null;){if(l=e[et])return l;e=_h(e)}return t}e=l,l=e.parentNode}return null}function yn(e){if(e=e[et]||e[mn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ma(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function gn(e){var t=e[oo];return t||(t=e[oo]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[ha]=!0}var fo=new Set,ho={};function Zl(e,t){vn(e,t),vn(e+"Capture",t)}function vn(e,t){for(ho[e]=t,e=0;e<t.length;e++)fo.add(t[e])}var ey=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),mo={},po={};function ty(e){return yl.call(po,e)?!0:yl.call(mo,e)?!1:ey.test(e)?po[e]=!0:(mo[e]=!0,!1)}function wu(e,t,l){if(ty(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var n=t.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Ou(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Ft(e,t,l,n){if(n===null)e.removeAttribute(l);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+n)}}var dr,yo;function bn(e){if(dr===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);dr=t&&t[1]||"",yo=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+dr+e+yo}var hr=!1;function mr(e,t){if(!e||hr)return"";hr=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(t){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(C){var _=C}Reflect.construct(e,[],q)}else{try{q.call()}catch(C){_=C}e.call(q.prototype)}}else{try{throw Error()}catch(C){_=C}(q=e())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(C){if(C&&_&&typeof C.stack=="string")return[C.stack,_.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=n.DetermineComponentFrameRoot(),d=r[0],p=r[1];if(d&&p){var b=d.split(`
`),N=p.split(`
`);for(u=n=0;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;for(;u<N.length&&!N[u].includes("DetermineComponentFrameRoot");)u++;if(n===b.length||u===N.length)for(n=b.length-1,u=N.length-1;1<=n&&0<=u&&b[n]!==N[u];)u--;for(;1<=n&&0<=u;n--,u--)if(b[n]!==N[u]){if(n!==1||u!==1)do if(n--,u--,0>u||b[n]!==N[u]){var H=`
`+b[n].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=n&&0<=u);break}}}finally{hr=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?bn(l):""}function ly(e){switch(e.tag){case 26:case 27:case 5:return bn(e.type);case 16:return bn("Lazy");case 13:return bn("Suspense");case 19:return bn("SuspenseList");case 0:case 15:return mr(e.type,!1);case 11:return mr(e.type.render,!1);case 1:return mr(e.type,!0);case 31:return bn("Activity");default:return""}}function go(e){try{var t="";do t+=ly(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function At(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vo(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ny(e){var t=vo(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var u=l.get,r=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(d){n=""+d,r.call(this,d)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return n},setValue:function(d){n=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Nu(e){e._valueTracker||(e._valueTracker=ny(e))}function bo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),n="";return e&&(n=vo(e)?e.checked?"true":"false":e.value),e=n,e!==l?(t.setValue(e),!0):!1}function _u(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var ay=/[\n"\\]/g;function wt(e){return e.replace(ay,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function pr(e,t,l,n,u,r,d,p){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+At(t)):e.value!==""+At(t)&&(e.value=""+At(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?yr(e,d,At(t)):l!=null?yr(e,d,At(l)):n!=null&&e.removeAttribute("value"),u==null&&r!=null&&(e.defaultChecked=!!r),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+At(p):e.removeAttribute("name")}function So(e,t,l,n,u,r,d,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||l!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;l=l!=null?""+At(l):"",t=t!=null?""+At(t):l,p||t===e.value||(e.value=t),e.defaultValue=t}n=n??u,n=typeof n!="function"&&typeof n!="symbol"&&!!n,e.checked=p?e.checked:!!n,e.defaultChecked=!!n,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function yr(e,t,l){t==="number"&&_u(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Sn(e,t,l,n){if(e=e.options,t){t={};for(var u=0;u<l.length;u++)t["$"+l[u]]=!0;for(l=0;l<e.length;l++)u=t.hasOwnProperty("$"+e[l].value),e[l].selected!==u&&(e[l].selected=u),u&&n&&(e[l].defaultSelected=!0)}else{for(l=""+At(l),t=null,u=0;u<e.length;u++){if(e[u].value===l){e[u].selected=!0,n&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function xo(e,t,l){if(t!=null&&(t=""+At(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+At(l):""}function Eo(e,t,l,n){if(t==null){if(n!=null){if(l!=null)throw Error(s(92));if(Me(n)){if(1<n.length)throw Error(s(93));n=n[0]}l=n}l==null&&(l=""),t=l}l=At(t),e.defaultValue=l,n=e.textContent,n===l&&n!==""&&n!==null&&(e.value=n)}function xn(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var uy=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ro(e,t,l){var n=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?n?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":n?e.setProperty(t,l):typeof l!="number"||l===0||uy.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function To(e,t,l){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,l!=null){for(var n in l)!l.hasOwnProperty(n)||t!=null&&t.hasOwnProperty(n)||(n.indexOf("--")===0?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="");for(var u in t)n=t[u],t.hasOwnProperty(u)&&l[u]!==n&&Ro(e,u,n)}else for(var r in t)t.hasOwnProperty(r)&&Ro(e,r,t[r])}function gr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var iy=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ry=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function zu(e){return ry.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var vr=null;function br(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var En=null,Rn=null;function Ao(e){var t=yn(e);if(t&&(e=t.stateNode)){var l=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(pr(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+wt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var n=l[t];if(n!==e&&n.form===e.form){var u=n[st]||null;if(!u)throw Error(s(90));pr(n,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<l.length;t++)n=l[t],n.form===e.form&&bo(n)}break e;case"textarea":xo(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Sn(e,!!l.multiple,t,!1)}}}var Sr=!1;function wo(e,t,l){if(Sr)return e(t,l);Sr=!0;try{var n=e(t);return n}finally{if(Sr=!1,(En!==null||Rn!==null)&&(pi(),En&&(t=En,e=Rn,Rn=En=null,Ao(t),e)))for(t=0;t<e.length;t++)Ao(e[t])}}function pa(e,t){var l=e.stateNode;if(l===null)return null;var n=l[st]||null;if(n===null)return null;l=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(s(231,t,typeof l));return l}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xr=!1;if(Wt)try{var ya={};Object.defineProperty(ya,"passive",{get:function(){xr=!0}}),window.addEventListener("test",ya,ya),window.removeEventListener("test",ya,ya)}catch{xr=!1}var bl=null,Er=null,Cu=null;function Oo(){if(Cu)return Cu;var e,t=Er,l=t.length,n,u="value"in bl?bl.value:bl.textContent,r=u.length;for(e=0;e<l&&t[e]===u[e];e++);var d=l-e;for(n=1;n<=d&&t[l-n]===u[r-n];n++);return Cu=u.slice(e,1<n?1-n:void 0)}function Mu(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Du(){return!0}function No(){return!1}function ct(e){function t(l,n,u,r,d){this._reactName=l,this._targetInst=u,this.type=n,this.nativeEvent=r,this.target=d,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(l=e[p],this[p]=l?l(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Du:No,this.isPropagationStopped=No,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Du)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Du)},persist:function(){},isPersistent:Du}),t}var Kl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Uu=ct(Kl),ga=v({},Kl,{view:0,detail:0}),sy=ct(ga),Rr,Tr,va,ju=v({},ga,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:wr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==va&&(va&&e.type==="mousemove"?(Rr=e.screenX-va.screenX,Tr=e.screenY-va.screenY):Tr=Rr=0,va=e),Rr)},movementY:function(e){return"movementY"in e?e.movementY:Tr}}),_o=ct(ju),cy=v({},ju,{dataTransfer:0}),oy=ct(cy),fy=v({},ga,{relatedTarget:0}),Ar=ct(fy),dy=v({},Kl,{animationName:0,elapsedTime:0,pseudoElement:0}),hy=ct(dy),my=v({},Kl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),py=ct(my),yy=v({},Kl,{data:0}),zo=ct(yy),gy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},vy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},by={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=by[e])?!!t[e]:!1}function wr(){return Sy}var xy=v({},ga,{key:function(e){if(e.key){var t=gy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Mu(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?vy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:wr,charCode:function(e){return e.type==="keypress"?Mu(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Mu(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ey=ct(xy),Ry=v({},ju,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Co=ct(Ry),Ty=v({},ga,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:wr}),Ay=ct(Ty),wy=v({},Kl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Oy=ct(wy),Ny=v({},ju,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_y=ct(Ny),zy=v({},Kl,{newState:0,oldState:0}),Cy=ct(zy),My=[9,13,27,32],Or=Wt&&"CompositionEvent"in window,ba=null;Wt&&"documentMode"in document&&(ba=document.documentMode);var Dy=Wt&&"TextEvent"in window&&!ba,Mo=Wt&&(!Or||ba&&8<ba&&11>=ba),Do=" ",Uo=!1;function jo(e,t){switch(e){case"keyup":return My.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ho(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tn=!1;function Uy(e,t){switch(e){case"compositionend":return Ho(t);case"keypress":return t.which!==32?null:(Uo=!0,Do);case"textInput":return e=t.data,e===Do&&Uo?null:e;default:return null}}function jy(e,t){if(Tn)return e==="compositionend"||!Or&&jo(e,t)?(e=Oo(),Cu=Er=bl=null,Tn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mo&&t.locale!=="ko"?null:t.data;default:return null}}var Hy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Hy[e.type]:t==="textarea"}function Lo(e,t,l,n){En?Rn?Rn.push(n):Rn=[n]:En=n,t=xi(t,"onChange"),0<t.length&&(l=new Uu("onChange","change",null,l,n),e.push({event:l,listeners:t}))}var Sa=null,xa=null;function By(e){bh(e,0)}function Hu(e){var t=ma(e);if(bo(t))return e}function qo(e,t){if(e==="change")return t}var ko=!1;if(Wt){var Nr;if(Wt){var _r="oninput"in document;if(!_r){var Yo=document.createElement("div");Yo.setAttribute("oninput","return;"),_r=typeof Yo.oninput=="function"}Nr=_r}else Nr=!1;ko=Nr&&(!document.documentMode||9<document.documentMode)}function Go(){Sa&&(Sa.detachEvent("onpropertychange",Vo),xa=Sa=null)}function Vo(e){if(e.propertyName==="value"&&Hu(xa)){var t=[];Lo(t,xa,e,br(e)),wo(By,t)}}function Ly(e,t,l){e==="focusin"?(Go(),Sa=t,xa=l,Sa.attachEvent("onpropertychange",Vo)):e==="focusout"&&Go()}function qy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Hu(xa)}function ky(e,t){if(e==="click")return Hu(t)}function Yy(e,t){if(e==="input"||e==="change")return Hu(t)}function Gy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vt=typeof Object.is=="function"?Object.is:Gy;function Ea(e,t){if(vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),n=Object.keys(t);if(l.length!==n.length)return!1;for(n=0;n<l.length;n++){var u=l[n];if(!yl.call(t,u)||!vt(e[u],t[u]))return!1}return!0}function Xo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Qo(e,t){var l=Xo(e);e=0;for(var n;l;){if(l.nodeType===3){if(n=e+l.textContent.length,e<=t&&n>=t)return{node:l,offset:t-e};e=n}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Xo(l)}}function Zo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Zo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ko(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=_u(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=_u(e.document)}return t}function zr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Vy=Wt&&"documentMode"in document&&11>=document.documentMode,An=null,Cr=null,Ra=null,Mr=!1;function Jo(e,t,l){var n=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Mr||An==null||An!==_u(n)||(n=An,"selectionStart"in n&&zr(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Ra&&Ea(Ra,n)||(Ra=n,n=xi(Cr,"onSelect"),0<n.length&&(t=new Uu("onSelect","select",null,t,l),e.push({event:t,listeners:n}),t.target=An)))}function Jl(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var wn={animationend:Jl("Animation","AnimationEnd"),animationiteration:Jl("Animation","AnimationIteration"),animationstart:Jl("Animation","AnimationStart"),transitionrun:Jl("Transition","TransitionRun"),transitionstart:Jl("Transition","TransitionStart"),transitioncancel:Jl("Transition","TransitionCancel"),transitionend:Jl("Transition","TransitionEnd")},Dr={},$o={};Wt&&($o=document.createElement("div").style,"AnimationEvent"in window||(delete wn.animationend.animation,delete wn.animationiteration.animation,delete wn.animationstart.animation),"TransitionEvent"in window||delete wn.transitionend.transition);function $l(e){if(Dr[e])return Dr[e];if(!wn[e])return e;var t=wn[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in $o)return Dr[e]=t[l];return e}var Fo=$l("animationend"),Wo=$l("animationiteration"),Po=$l("animationstart"),Xy=$l("transitionrun"),Qy=$l("transitionstart"),Zy=$l("transitioncancel"),Io=$l("transitionend"),ef=new Map,Ur="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ur.push("scrollEnd");function jt(e,t){ef.set(e,t),Zl(t,[e])}var tf=new WeakMap;function Ot(e,t){if(typeof e=="object"&&e!==null){var l=tf.get(e);return l!==void 0?l:(t={value:e,source:t,stack:go(t)},tf.set(e,t),t)}return{value:e,source:t,stack:go(t)}}var Nt=[],On=0,jr=0;function Bu(){for(var e=On,t=jr=On=0;t<e;){var l=Nt[t];Nt[t++]=null;var n=Nt[t];Nt[t++]=null;var u=Nt[t];Nt[t++]=null;var r=Nt[t];if(Nt[t++]=null,n!==null&&u!==null){var d=n.pending;d===null?u.next=u:(u.next=d.next,d.next=u),n.pending=u}r!==0&&lf(l,u,r)}}function Lu(e,t,l,n){Nt[On++]=e,Nt[On++]=t,Nt[On++]=l,Nt[On++]=n,jr|=n,e.lanes|=n,e=e.alternate,e!==null&&(e.lanes|=n)}function Hr(e,t,l,n){return Lu(e,t,l,n),qu(e)}function Nn(e,t){return Lu(e,null,null,t),qu(e)}function lf(e,t,l){e.lanes|=l;var n=e.alternate;n!==null&&(n.lanes|=l);for(var u=!1,r=e.return;r!==null;)r.childLanes|=l,n=r.alternate,n!==null&&(n.childLanes|=l),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(u=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,u&&t!==null&&(u=31-gt(l),e=r.hiddenUpdates,n=e[u],n===null?e[u]=[t]:n.push(t),t.lane=l|536870912),r):null}function qu(e){if(50<Ja)throw Ja=0,Gs=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var _n={};function Ky(e,t,l,n){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function bt(e,t,l,n){return new Ky(e,t,l,n)}function Br(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Pt(e,t){var l=e.alternate;return l===null?(l=bt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function nf(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ku(e,t,l,n,u,r){var d=0;if(n=e,typeof e=="function")Br(e)&&(d=1);else if(typeof e=="string")d=$0(e,l,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case he:return e=bt(31,l,t,u),e.elementType=he,e.lanes=r,e;case R:return Fl(l.children,u,r,t);case z:d=8,u|=24;break;case U:return e=bt(12,l,t,u|2),e.elementType=U,e.lanes=r,e;case K:return e=bt(13,l,t,u),e.elementType=K,e.lanes=r,e;case ce:return e=bt(19,l,t,u),e.elementType=ce,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case B:case Q:d=10;break e;case G:d=9;break e;case le:d=11;break e;case J:d=14;break e;case $:d=16,n=null;break e}d=29,l=Error(s(130,e===null?"null":typeof e,"")),n=null}return t=bt(d,l,t,u),t.elementType=e,t.type=n,t.lanes=r,t}function Fl(e,t,l,n){return e=bt(7,e,n,t),e.lanes=l,e}function Lr(e,t,l){return e=bt(6,e,null,t),e.lanes=l,e}function qr(e,t,l){return t=bt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var zn=[],Cn=0,Yu=null,Gu=0,_t=[],zt=0,Wl=null,It=1,el="";function Pl(e,t){zn[Cn++]=Gu,zn[Cn++]=Yu,Yu=e,Gu=t}function af(e,t,l){_t[zt++]=It,_t[zt++]=el,_t[zt++]=Wl,Wl=e;var n=It;e=el;var u=32-gt(n)-1;n&=~(1<<u),l+=1;var r=32-gt(t)+u;if(30<r){var d=u-u%5;r=(n&(1<<d)-1).toString(32),n>>=d,u-=d,It=1<<32-gt(t)+u|l<<u|n,el=r+e}else It=1<<r|l<<u|n,el=e}function kr(e){e.return!==null&&(Pl(e,1),af(e,1,0))}function Yr(e){for(;e===Yu;)Yu=zn[--Cn],zn[Cn]=null,Gu=zn[--Cn],zn[Cn]=null;for(;e===Wl;)Wl=_t[--zt],_t[zt]=null,el=_t[--zt],_t[zt]=null,It=_t[--zt],_t[zt]=null}var it=null,Ue=null,Se=!1,Il=null,Vt=!1,Gr=Error(s(519));function en(e){var t=Error(s(418,""));throw wa(Ot(t,e)),Gr}function uf(e){var t=e.stateNode,l=e.type,n=e.memoizedProps;switch(t[et]=e,t[st]=n,l){case"dialog":ye("cancel",t),ye("close",t);break;case"iframe":case"object":case"embed":ye("load",t);break;case"video":case"audio":for(l=0;l<Fa.length;l++)ye(Fa[l],t);break;case"source":ye("error",t);break;case"img":case"image":case"link":ye("error",t),ye("load",t);break;case"details":ye("toggle",t);break;case"input":ye("invalid",t),So(t,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Nu(t);break;case"select":ye("invalid",t);break;case"textarea":ye("invalid",t),Eo(t,n.value,n.defaultValue,n.children),Nu(t)}l=n.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||n.suppressHydrationWarning===!0||Rh(t.textContent,l)?(n.popover!=null&&(ye("beforetoggle",t),ye("toggle",t)),n.onScroll!=null&&ye("scroll",t),n.onScrollEnd!=null&&ye("scrollend",t),n.onClick!=null&&(t.onclick=Ei),t=!0):t=!1,t||en(e)}function rf(e){for(it=e.return;it;)switch(it.tag){case 5:case 13:Vt=!1;return;case 27:case 3:Vt=!0;return;default:it=it.return}}function Ta(e){if(e!==it)return!1;if(!Se)return rf(e),Se=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||ac(e.type,e.memoizedProps)),l=!l),l&&Ue&&en(e),rf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){Ue=Bt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}Ue=null}}else t===27?(t=Ue,jl(e.type)?(e=sc,sc=null,Ue=e):Ue=t):Ue=it?Bt(e.stateNode.nextSibling):null;return!0}function Aa(){Ue=it=null,Se=!1}function sf(){var e=Il;return e!==null&&(dt===null?dt=e:dt.push.apply(dt,e),Il=null),e}function wa(e){Il===null?Il=[e]:Il.push(e)}var Vr=Y(null),tn=null,tl=null;function Sl(e,t,l){X(Vr,t._currentValue),t._currentValue=l}function ll(e){e._currentValue=Vr.current,F(Vr)}function Xr(e,t,l){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===l)break;e=e.return}}function Qr(e,t,l,n){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var r=u.dependencies;if(r!==null){var d=u.child;r=r.firstContext;e:for(;r!==null;){var p=r;r=u;for(var b=0;b<t.length;b++)if(p.context===t[b]){r.lanes|=l,p=r.alternate,p!==null&&(p.lanes|=l),Xr(r.return,l,e),n||(d=null);break e}r=p.next}}else if(u.tag===18){if(d=u.return,d===null)throw Error(s(341));d.lanes|=l,r=d.alternate,r!==null&&(r.lanes|=l),Xr(d,l,e),d=null}else d=u.child;if(d!==null)d.return=u;else for(d=u;d!==null;){if(d===e){d=null;break}if(u=d.sibling,u!==null){u.return=d.return,d=u;break}d=d.return}u=d}}function Oa(e,t,l,n){e=null;for(var u=t,r=!1;u!==null;){if(!r){if((u.flags&524288)!==0)r=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var d=u.alternate;if(d===null)throw Error(s(387));if(d=d.memoizedProps,d!==null){var p=u.type;vt(u.pendingProps.value,d.value)||(e!==null?e.push(p):e=[p])}}else if(u===ve.current){if(d=u.alternate,d===null)throw Error(s(387));d.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(lu):e=[lu])}u=u.return}e!==null&&Qr(t,e,l,n),t.flags|=262144}function Vu(e){for(e=e.firstContext;e!==null;){if(!vt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ln(e){tn=e,tl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function tt(e){return cf(tn,e)}function Xu(e,t){return tn===null&&ln(e),cf(e,t)}function cf(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},tl===null){if(e===null)throw Error(s(308));tl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else tl=tl.next=t;return l}var Jy=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},$y=a.unstable_scheduleCallback,Fy=a.unstable_NormalPriority,Ge={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zr(){return{controller:new Jy,data:new Map,refCount:0}}function Na(e){e.refCount--,e.refCount===0&&$y(Fy,function(){e.controller.abort()})}var _a=null,Kr=0,Mn=0,Dn=null;function Wy(e,t){if(_a===null){var l=_a=[];Kr=0,Mn=$s(),Dn={status:"pending",value:void 0,then:function(n){l.push(n)}}}return Kr++,t.then(of,of),t}function of(){if(--Kr===0&&_a!==null){Dn!==null&&(Dn.status="fulfilled");var e=_a;_a=null,Mn=0,Dn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Py(e,t){var l=[],n={status:"pending",value:null,reason:null,then:function(u){l.push(u)}};return e.then(function(){n.status="fulfilled",n.value=t;for(var u=0;u<l.length;u++)(0,l[u])(t)},function(u){for(n.status="rejected",n.reason=u,u=0;u<l.length;u++)(0,l[u])(void 0)}),n}var ff=j.S;j.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Wy(e,t),ff!==null&&ff(e,t)};var nn=Y(null);function Jr(){var e=nn.current;return e!==null?e:_e.pooledCache}function Qu(e,t){t===null?X(nn,nn.current):X(nn,t.pool)}function df(){var e=Jr();return e===null?null:{parent:Ge._currentValue,pool:e}}var za=Error(s(460)),hf=Error(s(474)),Zu=Error(s(542)),$r={then:function(){}};function mf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ku(){}function pf(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Ku,Ku),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,gf(e),e;default:if(typeof t.status=="string")t.then(Ku,Ku);else{if(e=_e,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(n){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=n}},function(n){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=n}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,gf(e),e}throw Ca=t,za}}var Ca=null;function yf(){if(Ca===null)throw Error(s(459));var e=Ca;return Ca=null,e}function gf(e){if(e===za||e===Zu)throw Error(s(483))}var xl=!1;function Fr(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Wr(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function El(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Rl(e,t,l){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(Ee&2)!==0){var u=n.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),n.pending=t,t=qu(e),lf(e,null,l),t}return Lu(e,n,t,l),qu(e)}function Ma(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var n=t.lanes;n&=e.pendingLanes,l|=n,t.lanes=l,so(e,l)}}function Pr(e,t){var l=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,l===n)){var u=null,r=null;if(l=l.firstBaseUpdate,l!==null){do{var d={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};r===null?u=r=d:r=r.next=d,l=l.next}while(l!==null);r===null?u=r=t:r=r.next=t}else u=r=t;l={baseState:n.baseState,firstBaseUpdate:u,lastBaseUpdate:r,shared:n.shared,callbacks:n.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Ir=!1;function Da(){if(Ir){var e=Dn;if(e!==null)throw e}}function Ua(e,t,l,n){Ir=!1;var u=e.updateQueue;xl=!1;var r=u.firstBaseUpdate,d=u.lastBaseUpdate,p=u.shared.pending;if(p!==null){u.shared.pending=null;var b=p,N=b.next;b.next=null,d===null?r=N:d.next=N,d=b;var H=e.alternate;H!==null&&(H=H.updateQueue,p=H.lastBaseUpdate,p!==d&&(p===null?H.firstBaseUpdate=N:p.next=N,H.lastBaseUpdate=b))}if(r!==null){var q=u.baseState;d=0,H=N=b=null,p=r;do{var _=p.lane&-536870913,C=_!==p.lane;if(C?(ge&_)===_:(n&_)===_){_!==0&&_===Mn&&(Ir=!0),H!==null&&(H=H.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var ue=e,ne=p;_=t;var we=l;switch(ne.tag){case 1:if(ue=ne.payload,typeof ue=="function"){q=ue.call(we,q,_);break e}q=ue;break e;case 3:ue.flags=ue.flags&-65537|128;case 0:if(ue=ne.payload,_=typeof ue=="function"?ue.call(we,q,_):ue,_==null)break e;q=v({},q,_);break e;case 2:xl=!0}}_=p.callback,_!==null&&(e.flags|=64,C&&(e.flags|=8192),C=u.callbacks,C===null?u.callbacks=[_]:C.push(_))}else C={lane:_,tag:p.tag,payload:p.payload,callback:p.callback,next:null},H===null?(N=H=C,b=q):H=H.next=C,d|=_;if(p=p.next,p===null){if(p=u.shared.pending,p===null)break;C=p,p=C.next,C.next=null,u.lastBaseUpdate=C,u.shared.pending=null}}while(!0);H===null&&(b=q),u.baseState=b,u.firstBaseUpdate=N,u.lastBaseUpdate=H,r===null&&(u.shared.lanes=0),Cl|=d,e.lanes=d,e.memoizedState=q}}function vf(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function bf(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)vf(l[e],t)}var Un=Y(null),Ju=Y(0);function Sf(e,t){e=cl,X(Ju,e),X(Un,t),cl=e|t.baseLanes}function es(){X(Ju,cl),X(Un,Un.current)}function ts(){cl=Ju.current,F(Un),F(Ju)}var Tl=0,oe=null,Te=null,ke=null,$u=!1,jn=!1,an=!1,Fu=0,ja=0,Hn=null,Iy=0;function Be(){throw Error(s(321))}function ls(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!vt(e[l],t[l]))return!1;return!0}function ns(e,t,l,n,u,r){return Tl=r,oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,j.H=e===null||e.memoizedState===null?nd:ad,an=!1,r=l(n,u),an=!1,jn&&(r=Ef(t,l,n,u)),xf(e),r}function xf(e){j.H=li;var t=Te!==null&&Te.next!==null;if(Tl=0,ke=Te=oe=null,$u=!1,ja=0,Hn=null,t)throw Error(s(300));e===null||Ke||(e=e.dependencies,e!==null&&Vu(e)&&(Ke=!0))}function Ef(e,t,l,n){oe=e;var u=0;do{if(jn&&(Hn=null),ja=0,jn=!1,25<=u)throw Error(s(301));if(u+=1,ke=Te=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}j.H=i0,r=t(l,n)}while(jn);return r}function e0(){var e=j.H,t=e.useState()[0];return t=typeof t.then=="function"?Ha(t):t,e=e.useState()[0],(Te!==null?Te.memoizedState:null)!==e&&(oe.flags|=1024),t}function as(){var e=Fu!==0;return Fu=0,e}function us(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function is(e){if($u){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}$u=!1}Tl=0,ke=Te=oe=null,jn=!1,ja=Fu=0,Hn=null}function ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?oe.memoizedState=ke=e:ke=ke.next=e,ke}function Ye(){if(Te===null){var e=oe.alternate;e=e!==null?e.memoizedState:null}else e=Te.next;var t=ke===null?oe.memoizedState:ke.next;if(t!==null)ke=t,Te=e;else{if(e===null)throw oe.alternate===null?Error(s(467)):Error(s(310));Te=e,e={memoizedState:Te.memoizedState,baseState:Te.baseState,baseQueue:Te.baseQueue,queue:Te.queue,next:null},ke===null?oe.memoizedState=ke=e:ke=ke.next=e}return ke}function rs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ha(e){var t=ja;return ja+=1,Hn===null&&(Hn=[]),e=pf(Hn,e,t),t=oe,(ke===null?t.memoizedState:ke.next)===null&&(t=t.alternate,j.H=t===null||t.memoizedState===null?nd:ad),e}function Wu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ha(e);if(e.$$typeof===Q)return tt(e)}throw Error(s(438,String(e)))}function ss(e){var t=null,l=oe.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var n=oe.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(t={data:n.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=rs(),oe.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),n=0;n<e;n++)l[n]=Qe;return t.index++,l}function nl(e,t){return typeof t=="function"?t(e):t}function Pu(e){var t=Ye();return cs(t,Te,e)}function cs(e,t,l){var n=e.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=l;var u=e.baseQueue,r=n.pending;if(r!==null){if(u!==null){var d=u.next;u.next=r.next,r.next=d}t.baseQueue=u=r,n.pending=null}if(r=e.baseState,u===null)e.memoizedState=r;else{t=u.next;var p=d=null,b=null,N=t,H=!1;do{var q=N.lane&-536870913;if(q!==N.lane?(ge&q)===q:(Tl&q)===q){var _=N.revertLane;if(_===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),q===Mn&&(H=!0);else if((Tl&_)===_){N=N.next,_===Mn&&(H=!0);continue}else q={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},b===null?(p=b=q,d=r):b=b.next=q,oe.lanes|=_,Cl|=_;q=N.action,an&&l(r,q),r=N.hasEagerState?N.eagerState:l(r,q)}else _={lane:q,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},b===null?(p=b=_,d=r):b=b.next=_,oe.lanes|=q,Cl|=q;N=N.next}while(N!==null&&N!==t);if(b===null?d=r:b.next=p,!vt(r,e.memoizedState)&&(Ke=!0,H&&(l=Dn,l!==null)))throw l;e.memoizedState=r,e.baseState=d,e.baseQueue=b,n.lastRenderedState=r}return u===null&&(n.lanes=0),[e.memoizedState,n.dispatch]}function os(e){var t=Ye(),l=t.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=e;var n=l.dispatch,u=l.pending,r=t.memoizedState;if(u!==null){l.pending=null;var d=u=u.next;do r=e(r,d.action),d=d.next;while(d!==u);vt(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),l.lastRenderedState=r}return[r,n]}function Rf(e,t,l){var n=oe,u=Ye(),r=Se;if(r){if(l===void 0)throw Error(s(407));l=l()}else l=t();var d=!vt((Te||u).memoizedState,l);d&&(u.memoizedState=l,Ke=!0),u=u.queue;var p=wf.bind(null,n,u,e);if(Ba(2048,8,p,[e]),u.getSnapshot!==t||d||ke!==null&&ke.memoizedState.tag&1){if(n.flags|=2048,Bn(9,Iu(),Af.bind(null,n,u,l,t),null),_e===null)throw Error(s(349));r||(Tl&124)!==0||Tf(n,t,l)}return l}function Tf(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=oe.updateQueue,t===null?(t=rs(),oe.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Af(e,t,l,n){t.value=l,t.getSnapshot=n,Of(t)&&Nf(e)}function wf(e,t,l){return l(function(){Of(t)&&Nf(e)})}function Of(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!vt(e,l)}catch{return!0}}function Nf(e){var t=Nn(e,2);t!==null&&Tt(t,e,2)}function fs(e){var t=ot();if(typeof e=="function"){var l=e;if(e=l(),an){gl(!0);try{l()}finally{gl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:nl,lastRenderedState:e},t}function _f(e,t,l,n){return e.baseState=l,cs(e,Te,typeof n=="function"?n:nl)}function t0(e,t,l,n,u){if(ti(e))throw Error(s(485));if(e=t.action,e!==null){var r={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){r.listeners.push(d)}};j.T!==null?l(!0):r.isTransition=!1,n(r),l=t.pending,l===null?(r.next=t.pending=r,zf(t,r)):(r.next=l.next,t.pending=l.next=r)}}function zf(e,t){var l=t.action,n=t.payload,u=e.state;if(t.isTransition){var r=j.T,d={};j.T=d;try{var p=l(u,n),b=j.S;b!==null&&b(d,p),Cf(e,t,p)}catch(N){ds(e,t,N)}finally{j.T=r}}else try{r=l(u,n),Cf(e,t,r)}catch(N){ds(e,t,N)}}function Cf(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(n){Mf(e,t,n)},function(n){return ds(e,t,n)}):Mf(e,t,l)}function Mf(e,t,l){t.status="fulfilled",t.value=l,Df(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,zf(e,l)))}function ds(e,t,l){var n=e.pending;if(e.pending=null,n!==null){n=n.next;do t.status="rejected",t.reason=l,Df(t),t=t.next;while(t!==n)}e.action=null}function Df(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Uf(e,t){return t}function jf(e,t){if(Se){var l=_e.formState;if(l!==null){e:{var n=oe;if(Se){if(Ue){t:{for(var u=Ue,r=Vt;u.nodeType!==8;){if(!r){u=null;break t}if(u=Bt(u.nextSibling),u===null){u=null;break t}}r=u.data,u=r==="F!"||r==="F"?u:null}if(u){Ue=Bt(u.nextSibling),n=u.data==="F!";break e}}en(n)}n=!1}n&&(t=l[0])}}return l=ot(),l.memoizedState=l.baseState=t,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Uf,lastRenderedState:t},l.queue=n,l=ed.bind(null,oe,n),n.dispatch=l,n=fs(!1),r=gs.bind(null,oe,!1,n.queue),n=ot(),u={state:t,dispatch:null,action:e,pending:null},n.queue=u,l=t0.bind(null,oe,u,r,l),u.dispatch=l,n.memoizedState=e,[t,l,!1]}function Hf(e){var t=Ye();return Bf(t,Te,e)}function Bf(e,t,l){if(t=cs(e,t,Uf)[0],e=Pu(nl)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var n=Ha(t)}catch(d){throw d===za?Zu:d}else n=t;t=Ye();var u=t.queue,r=u.dispatch;return l!==t.memoizedState&&(oe.flags|=2048,Bn(9,Iu(),l0.bind(null,u,l),null)),[n,r,e]}function l0(e,t){e.action=t}function Lf(e){var t=Ye(),l=Te;if(l!==null)return Bf(t,l,e);Ye(),t=t.memoizedState,l=Ye();var n=l.queue.dispatch;return l.memoizedState=e,[t,n,!1]}function Bn(e,t,l,n){return e={tag:e,create:l,deps:n,inst:t,next:null},t=oe.updateQueue,t===null&&(t=rs(),oe.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(n=l.next,l.next=e,e.next=n,t.lastEffect=e),e}function Iu(){return{destroy:void 0,resource:void 0}}function qf(){return Ye().memoizedState}function ei(e,t,l,n){var u=ot();n=n===void 0?null:n,oe.flags|=e,u.memoizedState=Bn(1|t,Iu(),l,n)}function Ba(e,t,l,n){var u=Ye();n=n===void 0?null:n;var r=u.memoizedState.inst;Te!==null&&n!==null&&ls(n,Te.memoizedState.deps)?u.memoizedState=Bn(t,r,l,n):(oe.flags|=e,u.memoizedState=Bn(1|t,r,l,n))}function kf(e,t){ei(8390656,8,e,t)}function Yf(e,t){Ba(2048,8,e,t)}function Gf(e,t){return Ba(4,2,e,t)}function Vf(e,t){return Ba(4,4,e,t)}function Xf(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qf(e,t,l){l=l!=null?l.concat([e]):null,Ba(4,4,Xf.bind(null,t,e),l)}function hs(){}function Zf(e,t){var l=Ye();t=t===void 0?null:t;var n=l.memoizedState;return t!==null&&ls(t,n[1])?n[0]:(l.memoizedState=[e,t],e)}function Kf(e,t){var l=Ye();t=t===void 0?null:t;var n=l.memoizedState;if(t!==null&&ls(t,n[1]))return n[0];if(n=e(),an){gl(!0);try{e()}finally{gl(!1)}}return l.memoizedState=[n,t],n}function ms(e,t,l){return l===void 0||(Tl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Fd(),oe.lanes|=e,Cl|=e,l)}function Jf(e,t,l,n){return vt(l,t)?l:Un.current!==null?(e=ms(e,l,n),vt(e,t)||(Ke=!0),e):(Tl&42)===0?(Ke=!0,e.memoizedState=l):(e=Fd(),oe.lanes|=e,Cl|=e,t)}function $f(e,t,l,n,u){var r=Z.p;Z.p=r!==0&&8>r?r:8;var d=j.T,p={};j.T=p,gs(e,!1,t,l);try{var b=u(),N=j.S;if(N!==null&&N(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var H=Py(b,n);La(e,t,H,Rt(e))}else La(e,t,n,Rt(e))}catch(q){La(e,t,{then:function(){},status:"rejected",reason:q},Rt())}finally{Z.p=r,j.T=d}}function n0(){}function ps(e,t,l,n){if(e.tag!==5)throw Error(s(476));var u=Ff(e).queue;$f(e,u,t,V,l===null?n0:function(){return Wf(e),l(n)})}function Ff(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nl,lastRenderedState:V},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nl,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Wf(e){var t=Ff(e).next.queue;La(e,t,{},Rt())}function ys(){return tt(lu)}function Pf(){return Ye().memoizedState}function If(){return Ye().memoizedState}function a0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=Rt();e=El(l);var n=Rl(t,e,l);n!==null&&(Tt(n,t,l),Ma(n,t,l)),t={cache:Zr()},e.payload=t;return}t=t.return}}function u0(e,t,l){var n=Rt();l={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},ti(e)?td(t,l):(l=Hr(e,t,l,n),l!==null&&(Tt(l,e,n),ld(l,t,n)))}function ed(e,t,l){var n=Rt();La(e,t,l,n)}function La(e,t,l,n){var u={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(ti(e))td(t,u);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var d=t.lastRenderedState,p=r(d,l);if(u.hasEagerState=!0,u.eagerState=p,vt(p,d))return Lu(e,t,u,0),_e===null&&Bu(),!1}catch{}finally{}if(l=Hr(e,t,u,n),l!==null)return Tt(l,e,n),ld(l,t,n),!0}return!1}function gs(e,t,l,n){if(n={lane:2,revertLane:$s(),action:n,hasEagerState:!1,eagerState:null,next:null},ti(e)){if(t)throw Error(s(479))}else t=Hr(e,l,n,2),t!==null&&Tt(t,e,2)}function ti(e){var t=e.alternate;return e===oe||t!==null&&t===oe}function td(e,t){jn=$u=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function ld(e,t,l){if((l&4194048)!==0){var n=t.lanes;n&=e.pendingLanes,l|=n,t.lanes=l,so(e,l)}}var li={readContext:tt,use:Wu,useCallback:Be,useContext:Be,useEffect:Be,useImperativeHandle:Be,useLayoutEffect:Be,useInsertionEffect:Be,useMemo:Be,useReducer:Be,useRef:Be,useState:Be,useDebugValue:Be,useDeferredValue:Be,useTransition:Be,useSyncExternalStore:Be,useId:Be,useHostTransitionStatus:Be,useFormState:Be,useActionState:Be,useOptimistic:Be,useMemoCache:Be,useCacheRefresh:Be},nd={readContext:tt,use:Wu,useCallback:function(e,t){return ot().memoizedState=[e,t===void 0?null:t],e},useContext:tt,useEffect:kf,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,ei(4194308,4,Xf.bind(null,t,e),l)},useLayoutEffect:function(e,t){return ei(4194308,4,e,t)},useInsertionEffect:function(e,t){ei(4,2,e,t)},useMemo:function(e,t){var l=ot();t=t===void 0?null:t;var n=e();if(an){gl(!0);try{e()}finally{gl(!1)}}return l.memoizedState=[n,t],n},useReducer:function(e,t,l){var n=ot();if(l!==void 0){var u=l(t);if(an){gl(!0);try{l(t)}finally{gl(!1)}}}else u=t;return n.memoizedState=n.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},n.queue=e,e=e.dispatch=u0.bind(null,oe,e),[n.memoizedState,e]},useRef:function(e){var t=ot();return e={current:e},t.memoizedState=e},useState:function(e){e=fs(e);var t=e.queue,l=ed.bind(null,oe,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:hs,useDeferredValue:function(e,t){var l=ot();return ms(l,e,t)},useTransition:function(){var e=fs(!1);return e=$f.bind(null,oe,e.queue,!0,!1),ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var n=oe,u=ot();if(Se){if(l===void 0)throw Error(s(407));l=l()}else{if(l=t(),_e===null)throw Error(s(349));(ge&124)!==0||Tf(n,t,l)}u.memoizedState=l;var r={value:l,getSnapshot:t};return u.queue=r,kf(wf.bind(null,n,r,e),[e]),n.flags|=2048,Bn(9,Iu(),Af.bind(null,n,r,l,t),null),l},useId:function(){var e=ot(),t=_e.identifierPrefix;if(Se){var l=el,n=It;l=(n&~(1<<32-gt(n)-1)).toString(32)+l,t="«"+t+"R"+l,l=Fu++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=Iy++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ys,useFormState:jf,useActionState:jf,useOptimistic:function(e){var t=ot();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=gs.bind(null,oe,!0,l),l.dispatch=t,[e,t]},useMemoCache:ss,useCacheRefresh:function(){return ot().memoizedState=a0.bind(null,oe)}},ad={readContext:tt,use:Wu,useCallback:Zf,useContext:tt,useEffect:Yf,useImperativeHandle:Qf,useInsertionEffect:Gf,useLayoutEffect:Vf,useMemo:Kf,useReducer:Pu,useRef:qf,useState:function(){return Pu(nl)},useDebugValue:hs,useDeferredValue:function(e,t){var l=Ye();return Jf(l,Te.memoizedState,e,t)},useTransition:function(){var e=Pu(nl)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:Ha(e),t]},useSyncExternalStore:Rf,useId:Pf,useHostTransitionStatus:ys,useFormState:Hf,useActionState:Hf,useOptimistic:function(e,t){var l=Ye();return _f(l,Te,e,t)},useMemoCache:ss,useCacheRefresh:If},i0={readContext:tt,use:Wu,useCallback:Zf,useContext:tt,useEffect:Yf,useImperativeHandle:Qf,useInsertionEffect:Gf,useLayoutEffect:Vf,useMemo:Kf,useReducer:os,useRef:qf,useState:function(){return os(nl)},useDebugValue:hs,useDeferredValue:function(e,t){var l=Ye();return Te===null?ms(l,e,t):Jf(l,Te.memoizedState,e,t)},useTransition:function(){var e=os(nl)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:Ha(e),t]},useSyncExternalStore:Rf,useId:Pf,useHostTransitionStatus:ys,useFormState:Lf,useActionState:Lf,useOptimistic:function(e,t){var l=Ye();return Te!==null?_f(l,Te,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:ss,useCacheRefresh:If},Ln=null,qa=0;function ni(e){var t=qa;return qa+=1,Ln===null&&(Ln=[]),pf(Ln,e,t)}function ka(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function ai(e,t){throw t.$$typeof===A?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function ud(e){var t=e._init;return t(e._payload)}function id(e){function t(T,x){if(e){var w=T.deletions;w===null?(T.deletions=[x],T.flags|=16):w.push(x)}}function l(T,x){if(!e)return null;for(;x!==null;)t(T,x),x=x.sibling;return null}function n(T){for(var x=new Map;T!==null;)T.key!==null?x.set(T.key,T):x.set(T.index,T),T=T.sibling;return x}function u(T,x){return T=Pt(T,x),T.index=0,T.sibling=null,T}function r(T,x,w){return T.index=w,e?(w=T.alternate,w!==null?(w=w.index,w<x?(T.flags|=67108866,x):w):(T.flags|=67108866,x)):(T.flags|=1048576,x)}function d(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function p(T,x,w,L){return x===null||x.tag!==6?(x=Lr(w,T.mode,L),x.return=T,x):(x=u(x,w),x.return=T,x)}function b(T,x,w,L){var P=w.type;return P===R?H(T,x,w.props.children,L,w.key):x!==null&&(x.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===$&&ud(P)===x.type)?(x=u(x,w.props),ka(x,w),x.return=T,x):(x=ku(w.type,w.key,w.props,null,T.mode,L),ka(x,w),x.return=T,x)}function N(T,x,w,L){return x===null||x.tag!==4||x.stateNode.containerInfo!==w.containerInfo||x.stateNode.implementation!==w.implementation?(x=qr(w,T.mode,L),x.return=T,x):(x=u(x,w.children||[]),x.return=T,x)}function H(T,x,w,L,P){return x===null||x.tag!==7?(x=Fl(w,T.mode,L,P),x.return=T,x):(x=u(x,w),x.return=T,x)}function q(T,x,w){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=Lr(""+x,T.mode,w),x.return=T,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case O:return w=ku(x.type,x.key,x.props,null,T.mode,w),ka(w,x),w.return=T,w;case k:return x=qr(x,T.mode,w),x.return=T,x;case $:var L=x._init;return x=L(x._payload),q(T,x,w)}if(Me(x)||He(x))return x=Fl(x,T.mode,w,null),x.return=T,x;if(typeof x.then=="function")return q(T,ni(x),w);if(x.$$typeof===Q)return q(T,Xu(T,x),w);ai(T,x)}return null}function _(T,x,w,L){var P=x!==null?x.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return P!==null?null:p(T,x,""+w,L);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case O:return w.key===P?b(T,x,w,L):null;case k:return w.key===P?N(T,x,w,L):null;case $:return P=w._init,w=P(w._payload),_(T,x,w,L)}if(Me(w)||He(w))return P!==null?null:H(T,x,w,L,null);if(typeof w.then=="function")return _(T,x,ni(w),L);if(w.$$typeof===Q)return _(T,x,Xu(T,w),L);ai(T,w)}return null}function C(T,x,w,L,P){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return T=T.get(w)||null,p(x,T,""+L,P);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case O:return T=T.get(L.key===null?w:L.key)||null,b(x,T,L,P);case k:return T=T.get(L.key===null?w:L.key)||null,N(x,T,L,P);case $:var de=L._init;return L=de(L._payload),C(T,x,w,L,P)}if(Me(L)||He(L))return T=T.get(w)||null,H(x,T,L,P,null);if(typeof L.then=="function")return C(T,x,w,ni(L),P);if(L.$$typeof===Q)return C(T,x,w,Xu(x,L),P);ai(x,L)}return null}function ue(T,x,w,L){for(var P=null,de=null,te=x,ae=x=0,$e=null;te!==null&&ae<w.length;ae++){te.index>ae?($e=te,te=null):$e=te.sibling;var be=_(T,te,w[ae],L);if(be===null){te===null&&(te=$e);break}e&&te&&be.alternate===null&&t(T,te),x=r(be,x,ae),de===null?P=be:de.sibling=be,de=be,te=$e}if(ae===w.length)return l(T,te),Se&&Pl(T,ae),P;if(te===null){for(;ae<w.length;ae++)te=q(T,w[ae],L),te!==null&&(x=r(te,x,ae),de===null?P=te:de.sibling=te,de=te);return Se&&Pl(T,ae),P}for(te=n(te);ae<w.length;ae++)$e=C(te,T,ae,w[ae],L),$e!==null&&(e&&$e.alternate!==null&&te.delete($e.key===null?ae:$e.key),x=r($e,x,ae),de===null?P=$e:de.sibling=$e,de=$e);return e&&te.forEach(function(kl){return t(T,kl)}),Se&&Pl(T,ae),P}function ne(T,x,w,L){if(w==null)throw Error(s(151));for(var P=null,de=null,te=x,ae=x=0,$e=null,be=w.next();te!==null&&!be.done;ae++,be=w.next()){te.index>ae?($e=te,te=null):$e=te.sibling;var kl=_(T,te,be.value,L);if(kl===null){te===null&&(te=$e);break}e&&te&&kl.alternate===null&&t(T,te),x=r(kl,x,ae),de===null?P=kl:de.sibling=kl,de=kl,te=$e}if(be.done)return l(T,te),Se&&Pl(T,ae),P;if(te===null){for(;!be.done;ae++,be=w.next())be=q(T,be.value,L),be!==null&&(x=r(be,x,ae),de===null?P=be:de.sibling=be,de=be);return Se&&Pl(T,ae),P}for(te=n(te);!be.done;ae++,be=w.next())be=C(te,T,ae,be.value,L),be!==null&&(e&&be.alternate!==null&&te.delete(be.key===null?ae:be.key),x=r(be,x,ae),de===null?P=be:de.sibling=be,de=be);return e&&te.forEach(function(rg){return t(T,rg)}),Se&&Pl(T,ae),P}function we(T,x,w,L){if(typeof w=="object"&&w!==null&&w.type===R&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case O:e:{for(var P=w.key;x!==null;){if(x.key===P){if(P=w.type,P===R){if(x.tag===7){l(T,x.sibling),L=u(x,w.props.children),L.return=T,T=L;break e}}else if(x.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===$&&ud(P)===x.type){l(T,x.sibling),L=u(x,w.props),ka(L,w),L.return=T,T=L;break e}l(T,x);break}else t(T,x);x=x.sibling}w.type===R?(L=Fl(w.props.children,T.mode,L,w.key),L.return=T,T=L):(L=ku(w.type,w.key,w.props,null,T.mode,L),ka(L,w),L.return=T,T=L)}return d(T);case k:e:{for(P=w.key;x!==null;){if(x.key===P)if(x.tag===4&&x.stateNode.containerInfo===w.containerInfo&&x.stateNode.implementation===w.implementation){l(T,x.sibling),L=u(x,w.children||[]),L.return=T,T=L;break e}else{l(T,x);break}else t(T,x);x=x.sibling}L=qr(w,T.mode,L),L.return=T,T=L}return d(T);case $:return P=w._init,w=P(w._payload),we(T,x,w,L)}if(Me(w))return ue(T,x,w,L);if(He(w)){if(P=He(w),typeof P!="function")throw Error(s(150));return w=P.call(w),ne(T,x,w,L)}if(typeof w.then=="function")return we(T,x,ni(w),L);if(w.$$typeof===Q)return we(T,x,Xu(T,w),L);ai(T,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,x!==null&&x.tag===6?(l(T,x.sibling),L=u(x,w),L.return=T,T=L):(l(T,x),L=Lr(w,T.mode,L),L.return=T,T=L),d(T)):l(T,x)}return function(T,x,w,L){try{qa=0;var P=we(T,x,w,L);return Ln=null,P}catch(te){if(te===za||te===Zu)throw te;var de=bt(29,te,null,T.mode);return de.lanes=L,de.return=T,de}finally{}}}var qn=id(!0),rd=id(!1),Ct=Y(null),Xt=null;function Al(e){var t=e.alternate;X(Ve,Ve.current&1),X(Ct,e),Xt===null&&(t===null||Un.current!==null||t.memoizedState!==null)&&(Xt=e)}function sd(e){if(e.tag===22){if(X(Ve,Ve.current),X(Ct,e),Xt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Xt=e)}}else wl()}function wl(){X(Ve,Ve.current),X(Ct,Ct.current)}function al(e){F(Ct),Xt===e&&(Xt=null),F(Ve)}var Ve=Y(0);function ui(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||rc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function vs(e,t,l,n){t=e.memoizedState,l=l(n,t),l=l==null?t:v({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var bs={enqueueSetState:function(e,t,l){e=e._reactInternals;var n=Rt(),u=El(n);u.payload=t,l!=null&&(u.callback=l),t=Rl(e,u,n),t!==null&&(Tt(t,e,n),Ma(t,e,n))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var n=Rt(),u=El(n);u.tag=1,u.payload=t,l!=null&&(u.callback=l),t=Rl(e,u,n),t!==null&&(Tt(t,e,n),Ma(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=Rt(),n=El(l);n.tag=2,t!=null&&(n.callback=t),t=Rl(e,n,l),t!==null&&(Tt(t,e,l),Ma(t,e,l))}};function cd(e,t,l,n,u,r,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,r,d):t.prototype&&t.prototype.isPureReactComponent?!Ea(l,n)||!Ea(u,r):!0}function od(e,t,l,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,n),t.state!==e&&bs.enqueueReplaceState(t,t.state,null)}function un(e,t){var l=t;if("ref"in t){l={};for(var n in t)n!=="ref"&&(l[n]=t[n])}if(e=e.defaultProps){l===t&&(l=v({},l));for(var u in e)l[u]===void 0&&(l[u]=e[u])}return l}var ii=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function fd(e){ii(e)}function dd(e){console.error(e)}function hd(e){ii(e)}function ri(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function md(e,t,l){try{var n=e.onCaughtError;n(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function Ss(e,t,l){return l=El(l),l.tag=3,l.payload={element:null},l.callback=function(){ri(e,t)},l}function pd(e){return e=El(e),e.tag=3,e}function yd(e,t,l,n){var u=l.type.getDerivedStateFromError;if(typeof u=="function"){var r=n.value;e.payload=function(){return u(r)},e.callback=function(){md(t,l,n)}}var d=l.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){md(t,l,n),typeof u!="function"&&(Ml===null?Ml=new Set([this]):Ml.add(this));var p=n.stack;this.componentDidCatch(n.value,{componentStack:p!==null?p:""})})}function r0(e,t,l,n,u){if(l.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(t=l.alternate,t!==null&&Oa(t,l,u,!0),l=Ct.current,l!==null){switch(l.tag){case 13:return Xt===null?Xs():l.alternate===null&&je===0&&(je=3),l.flags&=-257,l.flags|=65536,l.lanes=u,n===$r?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([n]):t.add(n),Zs(e,n,u)),!1;case 22:return l.flags|=65536,n===$r?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([n])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([n]):l.add(n)),Zs(e,n,u)),!1}throw Error(s(435,l.tag))}return Zs(e,n,u),Xs(),!1}if(Se)return t=Ct.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,n!==Gr&&(e=Error(s(422),{cause:n}),wa(Ot(e,l)))):(n!==Gr&&(t=Error(s(423),{cause:n}),wa(Ot(t,l))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,n=Ot(n,l),u=Ss(e.stateNode,n,u),Pr(e,u),je!==4&&(je=2)),!1;var r=Error(s(520),{cause:n});if(r=Ot(r,l),Ka===null?Ka=[r]:Ka.push(r),je!==4&&(je=2),t===null)return!0;n=Ot(n,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=u&-u,l.lanes|=e,e=Ss(l.stateNode,n,e),Pr(l,e),!1;case 1:if(t=l.type,r=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Ml===null||!Ml.has(r))))return l.flags|=65536,u&=-u,l.lanes|=u,u=pd(u),yd(u,e,l,n),Pr(l,u),!1}l=l.return}while(l!==null);return!1}var gd=Error(s(461)),Ke=!1;function We(e,t,l,n){t.child=e===null?rd(t,null,l,n):qn(t,e.child,l,n)}function vd(e,t,l,n,u){l=l.render;var r=t.ref;if("ref"in n){var d={};for(var p in n)p!=="ref"&&(d[p]=n[p])}else d=n;return ln(t),n=ns(e,t,l,d,r,u),p=as(),e!==null&&!Ke?(us(e,t,u),ul(e,t,u)):(Se&&p&&kr(t),t.flags|=1,We(e,t,n,u),t.child)}function bd(e,t,l,n,u){if(e===null){var r=l.type;return typeof r=="function"&&!Br(r)&&r.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=r,Sd(e,t,r,n,u)):(e=ku(l.type,null,n,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!Ns(e,u)){var d=r.memoizedProps;if(l=l.compare,l=l!==null?l:Ea,l(d,n)&&e.ref===t.ref)return ul(e,t,u)}return t.flags|=1,e=Pt(r,n),e.ref=t.ref,e.return=t,t.child=e}function Sd(e,t,l,n,u){if(e!==null){var r=e.memoizedProps;if(Ea(r,n)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=n=r,Ns(e,u))(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,ul(e,t,u)}return xs(e,t,l,n,u)}function xd(e,t,l){var n=t.pendingProps,u=n.children,r=e!==null?e.memoizedState:null;if(n.mode==="hidden"){if((t.flags&128)!==0){if(n=r!==null?r.baseLanes|l:l,e!==null){for(u=t.child=e.child,r=0;u!==null;)r=r|u.lanes|u.childLanes,u=u.sibling;t.childLanes=r&~n}else t.childLanes=0,t.child=null;return Ed(e,t,n,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Qu(t,r!==null?r.cachePool:null),r!==null?Sf(t,r):es(),sd(t);else return t.lanes=t.childLanes=536870912,Ed(e,t,r!==null?r.baseLanes|l:l,l)}else r!==null?(Qu(t,r.cachePool),Sf(t,r),wl(),t.memoizedState=null):(e!==null&&Qu(t,null),es(),wl());return We(e,t,u,l),t.child}function Ed(e,t,l,n){var u=Jr();return u=u===null?null:{parent:Ge._currentValue,pool:u},t.memoizedState={baseLanes:l,cachePool:u},e!==null&&Qu(t,null),es(),sd(t),e!==null&&Oa(e,t,n,!0),null}function si(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function xs(e,t,l,n,u){return ln(t),l=ns(e,t,l,n,void 0,u),n=as(),e!==null&&!Ke?(us(e,t,u),ul(e,t,u)):(Se&&n&&kr(t),t.flags|=1,We(e,t,l,u),t.child)}function Rd(e,t,l,n,u,r){return ln(t),t.updateQueue=null,l=Ef(t,n,l,u),xf(e),n=as(),e!==null&&!Ke?(us(e,t,r),ul(e,t,r)):(Se&&n&&kr(t),t.flags|=1,We(e,t,l,r),t.child)}function Td(e,t,l,n,u){if(ln(t),t.stateNode===null){var r=_n,d=l.contextType;typeof d=="object"&&d!==null&&(r=tt(d)),r=new l(n,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=bs,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=n,r.state=t.memoizedState,r.refs={},Fr(t),d=l.contextType,r.context=typeof d=="object"&&d!==null?tt(d):_n,r.state=t.memoizedState,d=l.getDerivedStateFromProps,typeof d=="function"&&(vs(t,l,d,n),r.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(d=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),d!==r.state&&bs.enqueueReplaceState(r,r.state,null),Ua(t,n,r,u),Da(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),n=!0}else if(e===null){r=t.stateNode;var p=t.memoizedProps,b=un(l,p);r.props=b;var N=r.context,H=l.contextType;d=_n,typeof H=="object"&&H!==null&&(d=tt(H));var q=l.getDerivedStateFromProps;H=typeof q=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,H||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||N!==d)&&od(t,r,n,d),xl=!1;var _=t.memoizedState;r.state=_,Ua(t,n,r,u),Da(),N=t.memoizedState,p||_!==N||xl?(typeof q=="function"&&(vs(t,l,q,n),N=t.memoizedState),(b=xl||cd(t,l,b,n,_,N,d))?(H||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=N),r.props=n,r.state=N,r.context=d,n=b):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{r=t.stateNode,Wr(e,t),d=t.memoizedProps,H=un(l,d),r.props=H,q=t.pendingProps,_=r.context,N=l.contextType,b=_n,typeof N=="object"&&N!==null&&(b=tt(N)),p=l.getDerivedStateFromProps,(N=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(d!==q||_!==b)&&od(t,r,n,b),xl=!1,_=t.memoizedState,r.state=_,Ua(t,n,r,u),Da();var C=t.memoizedState;d!==q||_!==C||xl||e!==null&&e.dependencies!==null&&Vu(e.dependencies)?(typeof p=="function"&&(vs(t,l,p,n),C=t.memoizedState),(H=xl||cd(t,l,H,n,_,C,b)||e!==null&&e.dependencies!==null&&Vu(e.dependencies))?(N||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(n,C,b),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(n,C,b)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=C),r.props=n,r.state=C,r.context=b,n=H):(typeof r.componentDidUpdate!="function"||d===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),n=!1)}return r=n,si(e,t),n=(t.flags&128)!==0,r||n?(r=t.stateNode,l=n&&typeof l.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&n?(t.child=qn(t,e.child,null,u),t.child=qn(t,null,l,u)):We(e,t,l,u),t.memoizedState=r.state,e=t.child):e=ul(e,t,u),e}function Ad(e,t,l,n){return Aa(),t.flags|=256,We(e,t,l,n),t.child}var Es={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Rs(e){return{baseLanes:e,cachePool:df()}}function Ts(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Mt),e}function wd(e,t,l){var n=t.pendingProps,u=!1,r=(t.flags&128)!==0,d;if((d=r)||(d=e!==null&&e.memoizedState===null?!1:(Ve.current&2)!==0),d&&(u=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(u?Al(t):wl(),Se){var p=Ue,b;if(b=p){e:{for(b=p,p=Vt;b.nodeType!==8;){if(!p){p=null;break e}if(b=Bt(b.nextSibling),b===null){p=null;break e}}p=b}p!==null?(t.memoizedState={dehydrated:p,treeContext:Wl!==null?{id:It,overflow:el}:null,retryLane:536870912,hydrationErrors:null},b=bt(18,null,null,0),b.stateNode=p,b.return=t,t.child=b,it=t,Ue=null,b=!0):b=!1}b||en(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return rc(p)?t.lanes=32:t.lanes=536870912,null;al(t)}return p=n.children,n=n.fallback,u?(wl(),u=t.mode,p=ci({mode:"hidden",children:p},u),n=Fl(n,u,l,null),p.return=t,n.return=t,p.sibling=n,t.child=p,u=t.child,u.memoizedState=Rs(l),u.childLanes=Ts(e,d,l),t.memoizedState=Es,n):(Al(t),As(t,p))}if(b=e.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(r)t.flags&256?(Al(t),t.flags&=-257,t=ws(e,t,l)):t.memoizedState!==null?(wl(),t.child=e.child,t.flags|=128,t=null):(wl(),u=n.fallback,p=t.mode,n=ci({mode:"visible",children:n.children},p),u=Fl(u,p,l,null),u.flags|=2,n.return=t,u.return=t,n.sibling=u,t.child=n,qn(t,e.child,null,l),n=t.child,n.memoizedState=Rs(l),n.childLanes=Ts(e,d,l),t.memoizedState=Es,t=u);else if(Al(t),rc(p)){if(d=p.nextSibling&&p.nextSibling.dataset,d)var N=d.dgst;d=N,n=Error(s(419)),n.stack="",n.digest=d,wa({value:n,source:null,stack:null}),t=ws(e,t,l)}else if(Ke||Oa(e,t,l,!1),d=(l&e.childLanes)!==0,Ke||d){if(d=_e,d!==null&&(n=l&-l,n=(n&42)!==0?1:sr(n),n=(n&(d.suspendedLanes|l))!==0?0:n,n!==0&&n!==b.retryLane))throw b.retryLane=n,Nn(e,n),Tt(d,e,n),gd;p.data==="$?"||Xs(),t=ws(e,t,l)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Ue=Bt(p.nextSibling),it=t,Se=!0,Il=null,Vt=!1,e!==null&&(_t[zt++]=It,_t[zt++]=el,_t[zt++]=Wl,It=e.id,el=e.overflow,Wl=t),t=As(t,n.children),t.flags|=4096);return t}return u?(wl(),u=n.fallback,p=t.mode,b=e.child,N=b.sibling,n=Pt(b,{mode:"hidden",children:n.children}),n.subtreeFlags=b.subtreeFlags&65011712,N!==null?u=Pt(N,u):(u=Fl(u,p,l,null),u.flags|=2),u.return=t,n.return=t,n.sibling=u,t.child=n,n=u,u=t.child,p=e.child.memoizedState,p===null?p=Rs(l):(b=p.cachePool,b!==null?(N=Ge._currentValue,b=b.parent!==N?{parent:N,pool:N}:b):b=df(),p={baseLanes:p.baseLanes|l,cachePool:b}),u.memoizedState=p,u.childLanes=Ts(e,d,l),t.memoizedState=Es,n):(Al(t),l=e.child,e=l.sibling,l=Pt(l,{mode:"visible",children:n.children}),l.return=t,l.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=l,t.memoizedState=null,l)}function As(e,t){return t=ci({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ci(e,t){return e=bt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function ws(e,t,l){return qn(t,e.child,null,l),e=As(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Od(e,t,l){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Xr(e.return,t,l)}function Os(e,t,l,n,u){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:l,tailMode:u}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=n,r.tail=l,r.tailMode=u)}function Nd(e,t,l){var n=t.pendingProps,u=n.revealOrder,r=n.tail;if(We(e,t,n.children,l),n=Ve.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Od(e,l,t);else if(e.tag===19)Od(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}switch(X(Ve,n),u){case"forwards":for(l=t.child,u=null;l!==null;)e=l.alternate,e!==null&&ui(e)===null&&(u=l),l=l.sibling;l=u,l===null?(u=t.child,t.child=null):(u=l.sibling,l.sibling=null),Os(t,!1,u,l,r);break;case"backwards":for(l=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&ui(e)===null){t.child=u;break}e=u.sibling,u.sibling=l,l=u,u=e}Os(t,!0,l,null,r);break;case"together":Os(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ul(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),Cl|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Oa(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,l=Pt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Pt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Ns(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Vu(e)))}function s0(e,t,l){switch(t.tag){case 3:Oe(t,t.stateNode.containerInfo),Sl(t,Ge,e.memoizedState.cache),Aa();break;case 27:case 5:ml(t);break;case 4:Oe(t,t.stateNode.containerInfo);break;case 10:Sl(t,t.type,t.memoizedProps.value);break;case 13:var n=t.memoizedState;if(n!==null)return n.dehydrated!==null?(Al(t),t.flags|=128,null):(l&t.child.childLanes)!==0?wd(e,t,l):(Al(t),e=ul(e,t,l),e!==null?e.sibling:null);Al(t);break;case 19:var u=(e.flags&128)!==0;if(n=(l&t.childLanes)!==0,n||(Oa(e,t,l,!1),n=(l&t.childLanes)!==0),u){if(n)return Nd(e,t,l);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),X(Ve,Ve.current),n)break;return null;case 22:case 23:return t.lanes=0,xd(e,t,l);case 24:Sl(t,Ge,e.memoizedState.cache)}return ul(e,t,l)}function _d(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ke=!0;else{if(!Ns(e,l)&&(t.flags&128)===0)return Ke=!1,s0(e,t,l);Ke=(e.flags&131072)!==0}else Ke=!1,Se&&(t.flags&1048576)!==0&&af(t,Gu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var n=t.elementType,u=n._init;if(n=u(n._payload),t.type=n,typeof n=="function")Br(n)?(e=un(n,e),t.tag=1,t=Td(null,t,n,e,l)):(t.tag=0,t=xs(null,t,n,e,l));else{if(n!=null){if(u=n.$$typeof,u===le){t.tag=11,t=vd(null,t,n,e,l);break e}else if(u===J){t.tag=14,t=bd(null,t,n,e,l);break e}}throw t=Ut(n)||n,Error(s(306,t,""))}}return t;case 0:return xs(e,t,t.type,t.pendingProps,l);case 1:return n=t.type,u=un(n,t.pendingProps),Td(e,t,n,u,l);case 3:e:{if(Oe(t,t.stateNode.containerInfo),e===null)throw Error(s(387));n=t.pendingProps;var r=t.memoizedState;u=r.element,Wr(e,t),Ua(t,n,null,l);var d=t.memoizedState;if(n=d.cache,Sl(t,Ge,n),n!==r.cache&&Qr(t,[Ge],l,!0),Da(),n=d.element,r.isDehydrated)if(r={element:n,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=Ad(e,t,n,l);break e}else if(n!==u){u=Ot(Error(s(424)),t),wa(u),t=Ad(e,t,n,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ue=Bt(e.firstChild),it=t,Se=!0,Il=null,Vt=!0,l=rd(t,null,n,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Aa(),n===u){t=ul(e,t,l);break e}We(e,t,n,l)}t=t.child}return t;case 26:return si(e,t),e===null?(l=Dh(t.type,null,t.pendingProps,null))?t.memoizedState=l:Se||(l=t.type,e=t.pendingProps,n=Ri(ie.current).createElement(l),n[et]=t,n[st]=e,Ie(n,l,e),Ze(n),t.stateNode=n):t.memoizedState=Dh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ml(t),e===null&&Se&&(n=t.stateNode=zh(t.type,t.pendingProps,ie.current),it=t,Vt=!0,u=Ue,jl(t.type)?(sc=u,Ue=Bt(n.firstChild)):Ue=u),We(e,t,t.pendingProps.children,l),si(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((u=n=Ue)&&(n=H0(n,t.type,t.pendingProps,Vt),n!==null?(t.stateNode=n,it=t,Ue=Bt(n.firstChild),Vt=!1,u=!0):u=!1),u||en(t)),ml(t),u=t.type,r=t.pendingProps,d=e!==null?e.memoizedProps:null,n=r.children,ac(u,r)?n=null:d!==null&&ac(u,d)&&(t.flags|=32),t.memoizedState!==null&&(u=ns(e,t,e0,null,null,l),lu._currentValue=u),si(e,t),We(e,t,n,l),t.child;case 6:return e===null&&Se&&((e=l=Ue)&&(l=B0(l,t.pendingProps,Vt),l!==null?(t.stateNode=l,it=t,Ue=null,e=!0):e=!1),e||en(t)),null;case 13:return wd(e,t,l);case 4:return Oe(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=qn(t,null,n,l):We(e,t,n,l),t.child;case 11:return vd(e,t,t.type,t.pendingProps,l);case 7:return We(e,t,t.pendingProps,l),t.child;case 8:return We(e,t,t.pendingProps.children,l),t.child;case 12:return We(e,t,t.pendingProps.children,l),t.child;case 10:return n=t.pendingProps,Sl(t,t.type,n.value),We(e,t,n.children,l),t.child;case 9:return u=t.type._context,n=t.pendingProps.children,ln(t),u=tt(u),n=n(u),t.flags|=1,We(e,t,n,l),t.child;case 14:return bd(e,t,t.type,t.pendingProps,l);case 15:return Sd(e,t,t.type,t.pendingProps,l);case 19:return Nd(e,t,l);case 31:return n=t.pendingProps,l=t.mode,n={mode:n.mode,children:n.children},e===null?(l=ci(n,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Pt(e.child,n),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return xd(e,t,l);case 24:return ln(t),n=tt(Ge),e===null?(u=Jr(),u===null&&(u=_e,r=Zr(),u.pooledCache=r,r.refCount++,r!==null&&(u.pooledCacheLanes|=l),u=r),t.memoizedState={parent:n,cache:u},Fr(t),Sl(t,Ge,u)):((e.lanes&l)!==0&&(Wr(e,t),Ua(t,null,null,l),Da()),u=e.memoizedState,r=t.memoizedState,u.parent!==n?(u={parent:n,cache:n},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Sl(t,Ge,n)):(n=r.cache,Sl(t,Ge,n),n!==u.cache&&Qr(t,[Ge],l,!0))),We(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function il(e){e.flags|=4}function zd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Lh(t)){if(t=Ct.current,t!==null&&((ge&4194048)===ge?Xt!==null:(ge&62914560)!==ge&&(ge&536870912)===0||t!==Xt))throw Ca=$r,hf;e.flags|=8192}}function oi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?io():536870912,e.lanes|=t,Vn|=t)}function Ya(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var n=null;l!==null;)l.alternate!==null&&(n=l),l=l.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function De(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,n=0;if(t)for(var u=e.child;u!==null;)l|=u.lanes|u.childLanes,n|=u.subtreeFlags&65011712,n|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)l|=u.lanes|u.childLanes,n|=u.subtreeFlags,n|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=n,e.childLanes=l,t}function c0(e,t,l){var n=t.pendingProps;switch(Yr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return De(t),null;case 1:return De(t),null;case 3:return l=t.stateNode,n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ll(Ge),pt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Ta(t)?il(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,sf())),De(t),null;case 26:return l=t.memoizedState,e===null?(il(t),l!==null?(De(t),zd(t,l)):(De(t),t.flags&=-16777217)):l?l!==e.memoizedState?(il(t),De(t),zd(t,l)):(De(t),t.flags&=-16777217):(e.memoizedProps!==n&&il(t),De(t),t.flags&=-16777217),null;case 27:pl(t),l=ie.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==n&&il(t);else{if(!n){if(t.stateNode===null)throw Error(s(166));return De(t),null}e=W.current,Ta(t)?uf(t):(e=zh(u,n,l),t.stateNode=e,il(t))}return De(t),null;case 5:if(pl(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==n&&il(t);else{if(!n){if(t.stateNode===null)throw Error(s(166));return De(t),null}if(e=W.current,Ta(t))uf(t);else{switch(u=Ri(ie.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof n.is=="string"?u.createElement("select",{is:n.is}):u.createElement("select"),n.multiple?e.multiple=!0:n.size&&(e.size=n.size);break;default:e=typeof n.is=="string"?u.createElement(l,{is:n.is}):u.createElement(l)}}e[et]=t,e[st]=n;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Ie(e,l,n),l){case"button":case"input":case"select":case"textarea":e=!!n.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&il(t)}}return De(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==n&&il(t);else{if(typeof n!="string"&&t.stateNode===null)throw Error(s(166));if(e=ie.current,Ta(t)){if(e=t.stateNode,l=t.memoizedProps,n=null,u=it,u!==null)switch(u.tag){case 27:case 5:n=u.memoizedProps}e[et]=t,e=!!(e.nodeValue===l||n!==null&&n.suppressHydrationWarning===!0||Rh(e.nodeValue,l)),e||en(t)}else e=Ri(e).createTextNode(n),e[et]=t,t.stateNode=e}return De(t),null;case 13:if(n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=Ta(t),n!==null&&n.dehydrated!==null){if(e===null){if(!u)throw Error(s(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(s(317));u[et]=t}else Aa(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;De(t),u=!1}else u=sf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(al(t),t):(al(t),null)}if(al(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=n!==null,e=e!==null&&e.memoizedState!==null,l){n=t.child,u=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(u=n.alternate.memoizedState.cachePool.pool);var r=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(r=n.memoizedState.cachePool.pool),r!==u&&(n.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),oi(t,t.updateQueue),De(t),null;case 4:return pt(),e===null&&Is(t.stateNode.containerInfo),De(t),null;case 10:return ll(t.type),De(t),null;case 19:if(F(Ve),u=t.memoizedState,u===null)return De(t),null;if(n=(t.flags&128)!==0,r=u.rendering,r===null)if(n)Ya(u,!1);else{if(je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=ui(e),r!==null){for(t.flags|=128,Ya(u,!1),e=r.updateQueue,t.updateQueue=e,oi(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)nf(l,e),l=l.sibling;return X(Ve,Ve.current&1|2),t.child}e=e.sibling}u.tail!==null&&Gt()>hi&&(t.flags|=128,n=!0,Ya(u,!1),t.lanes=4194304)}else{if(!n)if(e=ui(r),e!==null){if(t.flags|=128,n=!0,e=e.updateQueue,t.updateQueue=e,oi(t,e),Ya(u,!0),u.tail===null&&u.tailMode==="hidden"&&!r.alternate&&!Se)return De(t),null}else 2*Gt()-u.renderingStartTime>hi&&l!==536870912&&(t.flags|=128,n=!0,Ya(u,!1),t.lanes=4194304);u.isBackwards?(r.sibling=t.child,t.child=r):(e=u.last,e!==null?e.sibling=r:t.child=r,u.last=r)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Gt(),t.sibling=null,e=Ve.current,X(Ve,n?e&1|2:e&1),t):(De(t),null);case 22:case 23:return al(t),ts(),n=t.memoizedState!==null,e!==null?e.memoizedState!==null!==n&&(t.flags|=8192):n&&(t.flags|=8192),n?(l&536870912)!==0&&(t.flags&128)===0&&(De(t),t.subtreeFlags&6&&(t.flags|=8192)):De(t),l=t.updateQueue,l!==null&&oi(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),n=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),n!==l&&(t.flags|=2048),e!==null&&F(nn),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),ll(Ge),De(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function o0(e,t){switch(Yr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ll(Ge),pt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return pl(t),null;case 13:if(al(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Aa()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(Ve),null;case 4:return pt(),null;case 10:return ll(t.type),null;case 22:case 23:return al(t),ts(),e!==null&&F(nn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ll(Ge),null;case 25:return null;default:return null}}function Cd(e,t){switch(Yr(t),t.tag){case 3:ll(Ge),pt();break;case 26:case 27:case 5:pl(t);break;case 4:pt();break;case 13:al(t);break;case 19:F(Ve);break;case 10:ll(t.type);break;case 22:case 23:al(t),ts(),e!==null&&F(nn);break;case 24:ll(Ge)}}function Ga(e,t){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&e)===e){n=void 0;var r=l.create,d=l.inst;n=r(),d.destroy=n}l=l.next}while(l!==u)}}catch(p){Ne(t,t.return,p)}}function Ol(e,t,l){try{var n=t.updateQueue,u=n!==null?n.lastEffect:null;if(u!==null){var r=u.next;n=r;do{if((n.tag&e)===e){var d=n.inst,p=d.destroy;if(p!==void 0){d.destroy=void 0,u=t;var b=l,N=p;try{N()}catch(H){Ne(u,b,H)}}}n=n.next}while(n!==r)}}catch(H){Ne(t,t.return,H)}}function Md(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{bf(t,l)}catch(n){Ne(e,e.return,n)}}}function Dd(e,t,l){l.props=un(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(n){Ne(e,t,n)}}function Va(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var n=e.stateNode;break;case 30:n=e.stateNode;break;default:n=e.stateNode}typeof l=="function"?e.refCleanup=l(n):l.current=n}}catch(u){Ne(e,t,u)}}function Qt(e,t){var l=e.ref,n=e.refCleanup;if(l!==null)if(typeof n=="function")try{n()}catch(u){Ne(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(u){Ne(e,t,u)}else l.current=null}function Ud(e){var t=e.type,l=e.memoizedProps,n=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break e;case"img":l.src?n.src=l.src:l.srcSet&&(n.srcset=l.srcSet)}}catch(u){Ne(e,e.return,u)}}function _s(e,t,l){try{var n=e.stateNode;C0(n,e.type,l,t),n[st]=t}catch(u){Ne(e,e.return,u)}}function jd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&jl(e.type)||e.tag===4}function zs(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||jd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&jl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Cs(e,t,l){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Ei));else if(n!==4&&(n===27&&jl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Cs(e,t,l),e=e.sibling;e!==null;)Cs(e,t,l),e=e.sibling}function fi(e,t,l){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(n!==4&&(n===27&&jl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(fi(e,t,l),e=e.sibling;e!==null;)fi(e,t,l),e=e.sibling}function Hd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var n=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Ie(t,n,l),t[et]=e,t[st]=l}catch(r){Ne(e,e.return,r)}}var rl=!1,Le=!1,Ms=!1,Bd=typeof WeakSet=="function"?WeakSet:Set,Je=null;function f0(e,t){if(e=e.containerInfo,lc=_i,e=Ko(e),zr(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var n=l.getSelection&&l.getSelection();if(n&&n.rangeCount!==0){l=n.anchorNode;var u=n.anchorOffset,r=n.focusNode;n=n.focusOffset;try{l.nodeType,r.nodeType}catch{l=null;break e}var d=0,p=-1,b=-1,N=0,H=0,q=e,_=null;t:for(;;){for(var C;q!==l||u!==0&&q.nodeType!==3||(p=d+u),q!==r||n!==0&&q.nodeType!==3||(b=d+n),q.nodeType===3&&(d+=q.nodeValue.length),(C=q.firstChild)!==null;)_=q,q=C;for(;;){if(q===e)break t;if(_===l&&++N===u&&(p=d),_===r&&++H===n&&(b=d),(C=q.nextSibling)!==null)break;q=_,_=q.parentNode}q=C}l=p===-1||b===-1?null:{start:p,end:b}}else l=null}l=l||{start:0,end:0}}else l=null;for(nc={focusedElem:e,selectionRange:l},_i=!1,Je=t;Je!==null;)if(t=Je,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Je=e;else for(;Je!==null;){switch(t=Je,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,l=t,u=r.memoizedProps,r=r.memoizedState,n=l.stateNode;try{var ue=un(l.type,u,l.elementType===l.type);e=n.getSnapshotBeforeUpdate(ue,r),n.__reactInternalSnapshotBeforeUpdate=e}catch(ne){Ne(l,l.return,ne)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)ic(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ic(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Je=e;break}Je=t.return}}function Ld(e,t,l){var n=l.flags;switch(l.tag){case 0:case 11:case 15:Nl(e,l),n&4&&Ga(5,l);break;case 1:if(Nl(e,l),n&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(d){Ne(l,l.return,d)}else{var u=un(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){Ne(l,l.return,d)}}n&64&&Md(l),n&512&&Va(l,l.return);break;case 3:if(Nl(e,l),n&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{bf(e,t)}catch(d){Ne(l,l.return,d)}}break;case 27:t===null&&n&4&&Hd(l);case 26:case 5:Nl(e,l),t===null&&n&4&&Ud(l),n&512&&Va(l,l.return);break;case 12:Nl(e,l);break;case 13:Nl(e,l),n&4&&Yd(e,l),n&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=S0.bind(null,l),L0(e,l))));break;case 22:if(n=l.memoizedState!==null||rl,!n){t=t!==null&&t.memoizedState!==null||Le,u=rl;var r=Le;rl=n,(Le=t)&&!r?_l(e,l,(l.subtreeFlags&8772)!==0):Nl(e,l),rl=u,Le=r}break;case 30:break;default:Nl(e,l)}}function qd(e){var t=e.alternate;t!==null&&(e.alternate=null,qd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&fr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ze=null,ft=!1;function sl(e,t,l){for(l=l.child;l!==null;)kd(e,t,l),l=l.sibling}function kd(e,t,l){if(yt&&typeof yt.onCommitFiberUnmount=="function")try{yt.onCommitFiberUnmount(oa,l)}catch{}switch(l.tag){case 26:Le||Qt(l,t),sl(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Le||Qt(l,t);var n=ze,u=ft;jl(l.type)&&(ze=l.stateNode,ft=!1),sl(e,t,l),Pa(l.stateNode),ze=n,ft=u;break;case 5:Le||Qt(l,t);case 6:if(n=ze,u=ft,ze=null,sl(e,t,l),ze=n,ft=u,ze!==null)if(ft)try{(ze.nodeType===9?ze.body:ze.nodeName==="HTML"?ze.ownerDocument.body:ze).removeChild(l.stateNode)}catch(r){Ne(l,t,r)}else try{ze.removeChild(l.stateNode)}catch(r){Ne(l,t,r)}break;case 18:ze!==null&&(ft?(e=ze,Nh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),iu(e)):Nh(ze,l.stateNode));break;case 4:n=ze,u=ft,ze=l.stateNode.containerInfo,ft=!0,sl(e,t,l),ze=n,ft=u;break;case 0:case 11:case 14:case 15:Le||Ol(2,l,t),Le||Ol(4,l,t),sl(e,t,l);break;case 1:Le||(Qt(l,t),n=l.stateNode,typeof n.componentWillUnmount=="function"&&Dd(l,t,n)),sl(e,t,l);break;case 21:sl(e,t,l);break;case 22:Le=(n=Le)||l.memoizedState!==null,sl(e,t,l),Le=n;break;default:sl(e,t,l)}}function Yd(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{iu(e)}catch(l){Ne(t,t.return,l)}}function d0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Bd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Bd),t;default:throw Error(s(435,e.tag))}}function Ds(e,t){var l=d0(e);t.forEach(function(n){var u=x0.bind(null,e,n);l.has(n)||(l.add(n),n.then(u,u))})}function St(e,t){var l=t.deletions;if(l!==null)for(var n=0;n<l.length;n++){var u=l[n],r=e,d=t,p=d;e:for(;p!==null;){switch(p.tag){case 27:if(jl(p.type)){ze=p.stateNode,ft=!1;break e}break;case 5:ze=p.stateNode,ft=!1;break e;case 3:case 4:ze=p.stateNode.containerInfo,ft=!0;break e}p=p.return}if(ze===null)throw Error(s(160));kd(r,d,u),ze=null,ft=!1,r=u.alternate,r!==null&&(r.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Gd(t,e),t=t.sibling}var Ht=null;function Gd(e,t){var l=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:St(t,e),xt(e),n&4&&(Ol(3,e,e.return),Ga(3,e),Ol(5,e,e.return));break;case 1:St(t,e),xt(e),n&512&&(Le||l===null||Qt(l,l.return)),n&64&&rl&&(e=e.updateQueue,e!==null&&(n=e.callbacks,n!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?n:l.concat(n))));break;case 26:var u=Ht;if(St(t,e),xt(e),n&512&&(Le||l===null||Qt(l,l.return)),n&4){var r=l!==null?l.memoizedState:null;if(n=e.memoizedState,l===null)if(n===null)if(e.stateNode===null){e:{n=e.type,l=e.memoizedProps,u=u.ownerDocument||u;t:switch(n){case"title":r=u.getElementsByTagName("title")[0],(!r||r[ha]||r[et]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=u.createElement(n),u.head.insertBefore(r,u.querySelector("head > title"))),Ie(r,n,l),r[et]=e,Ze(r),n=r;break e;case"link":var d=Hh("link","href",u).get(n+(l.href||""));if(d){for(var p=0;p<d.length;p++)if(r=d[p],r.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&r.getAttribute("rel")===(l.rel==null?null:l.rel)&&r.getAttribute("title")===(l.title==null?null:l.title)&&r.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){d.splice(p,1);break t}}r=u.createElement(n),Ie(r,n,l),u.head.appendChild(r);break;case"meta":if(d=Hh("meta","content",u).get(n+(l.content||""))){for(p=0;p<d.length;p++)if(r=d[p],r.getAttribute("content")===(l.content==null?null:""+l.content)&&r.getAttribute("name")===(l.name==null?null:l.name)&&r.getAttribute("property")===(l.property==null?null:l.property)&&r.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&r.getAttribute("charset")===(l.charSet==null?null:l.charSet)){d.splice(p,1);break t}}r=u.createElement(n),Ie(r,n,l),u.head.appendChild(r);break;default:throw Error(s(468,n))}r[et]=e,Ze(r),n=r}e.stateNode=n}else Bh(u,e.type,e.stateNode);else e.stateNode=jh(u,n,e.memoizedProps);else r!==n?(r===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):r.count--,n===null?Bh(u,e.type,e.stateNode):jh(u,n,e.memoizedProps)):n===null&&e.stateNode!==null&&_s(e,e.memoizedProps,l.memoizedProps)}break;case 27:St(t,e),xt(e),n&512&&(Le||l===null||Qt(l,l.return)),l!==null&&n&4&&_s(e,e.memoizedProps,l.memoizedProps);break;case 5:if(St(t,e),xt(e),n&512&&(Le||l===null||Qt(l,l.return)),e.flags&32){u=e.stateNode;try{xn(u,"")}catch(C){Ne(e,e.return,C)}}n&4&&e.stateNode!=null&&(u=e.memoizedProps,_s(e,u,l!==null?l.memoizedProps:u)),n&1024&&(Ms=!0);break;case 6:if(St(t,e),xt(e),n&4){if(e.stateNode===null)throw Error(s(162));n=e.memoizedProps,l=e.stateNode;try{l.nodeValue=n}catch(C){Ne(e,e.return,C)}}break;case 3:if(wi=null,u=Ht,Ht=Ti(t.containerInfo),St(t,e),Ht=u,xt(e),n&4&&l!==null&&l.memoizedState.isDehydrated)try{iu(t.containerInfo)}catch(C){Ne(e,e.return,C)}Ms&&(Ms=!1,Vd(e));break;case 4:n=Ht,Ht=Ti(e.stateNode.containerInfo),St(t,e),xt(e),Ht=n;break;case 12:St(t,e),xt(e);break;case 13:St(t,e),xt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(qs=Gt()),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,Ds(e,n)));break;case 22:u=e.memoizedState!==null;var b=l!==null&&l.memoizedState!==null,N=rl,H=Le;if(rl=N||u,Le=H||b,St(t,e),Le=H,rl=N,xt(e),n&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(l===null||b||rl||Le||rn(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){b=l=t;try{if(r=b.stateNode,u)d=r.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{p=b.stateNode;var q=b.memoizedProps.style,_=q!=null&&q.hasOwnProperty("display")?q.display:null;p.style.display=_==null||typeof _=="boolean"?"":(""+_).trim()}}catch(C){Ne(b,b.return,C)}}}else if(t.tag===6){if(l===null){b=t;try{b.stateNode.nodeValue=u?"":b.memoizedProps}catch(C){Ne(b,b.return,C)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}n&4&&(n=e.updateQueue,n!==null&&(l=n.retryQueue,l!==null&&(n.retryQueue=null,Ds(e,l))));break;case 19:St(t,e),xt(e),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,Ds(e,n)));break;case 30:break;case 21:break;default:St(t,e),xt(e)}}function xt(e){var t=e.flags;if(t&2){try{for(var l,n=e.return;n!==null;){if(jd(n)){l=n;break}n=n.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var u=l.stateNode,r=zs(e);fi(e,r,u);break;case 5:var d=l.stateNode;l.flags&32&&(xn(d,""),l.flags&=-33);var p=zs(e);fi(e,p,d);break;case 3:case 4:var b=l.stateNode.containerInfo,N=zs(e);Cs(e,N,b);break;default:throw Error(s(161))}}catch(H){Ne(e,e.return,H)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Vd(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Vd(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Nl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Ld(e,t.alternate,t),t=t.sibling}function rn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ol(4,t,t.return),rn(t);break;case 1:Qt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Dd(t,t.return,l),rn(t);break;case 27:Pa(t.stateNode);case 26:case 5:Qt(t,t.return),rn(t);break;case 22:t.memoizedState===null&&rn(t);break;case 30:rn(t);break;default:rn(t)}e=e.sibling}}function _l(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var n=t.alternate,u=e,r=t,d=r.flags;switch(r.tag){case 0:case 11:case 15:_l(u,r,l),Ga(4,r);break;case 1:if(_l(u,r,l),n=r,u=n.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(N){Ne(n,n.return,N)}if(n=r,u=n.updateQueue,u!==null){var p=n.stateNode;try{var b=u.shared.hiddenCallbacks;if(b!==null)for(u.shared.hiddenCallbacks=null,u=0;u<b.length;u++)vf(b[u],p)}catch(N){Ne(n,n.return,N)}}l&&d&64&&Md(r),Va(r,r.return);break;case 27:Hd(r);case 26:case 5:_l(u,r,l),l&&n===null&&d&4&&Ud(r),Va(r,r.return);break;case 12:_l(u,r,l);break;case 13:_l(u,r,l),l&&d&4&&Yd(u,r);break;case 22:r.memoizedState===null&&_l(u,r,l),Va(r,r.return);break;case 30:break;default:_l(u,r,l)}t=t.sibling}}function Us(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&Na(l))}function js(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Na(e))}function Zt(e,t,l,n){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Xd(e,t,l,n),t=t.sibling}function Xd(e,t,l,n){var u=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,l,n),u&2048&&Ga(9,t);break;case 1:Zt(e,t,l,n);break;case 3:Zt(e,t,l,n),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Na(e)));break;case 12:if(u&2048){Zt(e,t,l,n),e=t.stateNode;try{var r=t.memoizedProps,d=r.id,p=r.onPostCommit;typeof p=="function"&&p(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){Ne(t,t.return,b)}}else Zt(e,t,l,n);break;case 13:Zt(e,t,l,n);break;case 23:break;case 22:r=t.stateNode,d=t.alternate,t.memoizedState!==null?r._visibility&2?Zt(e,t,l,n):Xa(e,t):r._visibility&2?Zt(e,t,l,n):(r._visibility|=2,kn(e,t,l,n,(t.subtreeFlags&10256)!==0)),u&2048&&Us(d,t);break;case 24:Zt(e,t,l,n),u&2048&&js(t.alternate,t);break;default:Zt(e,t,l,n)}}function kn(e,t,l,n,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,d=t,p=l,b=n,N=d.flags;switch(d.tag){case 0:case 11:case 15:kn(r,d,p,b,u),Ga(8,d);break;case 23:break;case 22:var H=d.stateNode;d.memoizedState!==null?H._visibility&2?kn(r,d,p,b,u):Xa(r,d):(H._visibility|=2,kn(r,d,p,b,u)),u&&N&2048&&Us(d.alternate,d);break;case 24:kn(r,d,p,b,u),u&&N&2048&&js(d.alternate,d);break;default:kn(r,d,p,b,u)}t=t.sibling}}function Xa(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,n=t,u=n.flags;switch(n.tag){case 22:Xa(l,n),u&2048&&Us(n.alternate,n);break;case 24:Xa(l,n),u&2048&&js(n.alternate,n);break;default:Xa(l,n)}t=t.sibling}}var Qa=8192;function Yn(e){if(e.subtreeFlags&Qa)for(e=e.child;e!==null;)Qd(e),e=e.sibling}function Qd(e){switch(e.tag){case 26:Yn(e),e.flags&Qa&&e.memoizedState!==null&&W0(Ht,e.memoizedState,e.memoizedProps);break;case 5:Yn(e);break;case 3:case 4:var t=Ht;Ht=Ti(e.stateNode.containerInfo),Yn(e),Ht=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Qa,Qa=16777216,Yn(e),Qa=t):Yn(e));break;default:Yn(e)}}function Zd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Za(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var n=t[l];Je=n,Jd(n,e)}Zd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Kd(e),e=e.sibling}function Kd(e){switch(e.tag){case 0:case 11:case 15:Za(e),e.flags&2048&&Ol(9,e,e.return);break;case 3:Za(e);break;case 12:Za(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,di(e)):Za(e);break;default:Za(e)}}function di(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var n=t[l];Je=n,Jd(n,e)}Zd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ol(8,t,t.return),di(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,di(t));break;default:di(t)}e=e.sibling}}function Jd(e,t){for(;Je!==null;){var l=Je;switch(l.tag){case 0:case 11:case 15:Ol(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var n=l.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Na(l.memoizedState.cache)}if(n=l.child,n!==null)n.return=l,Je=n;else e:for(l=e;Je!==null;){n=Je;var u=n.sibling,r=n.return;if(qd(n),n===l){Je=null;break e}if(u!==null){u.return=r,Je=u;break e}Je=r}}}var h0={getCacheForType:function(e){var t=tt(Ge),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},m0=typeof WeakMap=="function"?WeakMap:Map,Ee=0,_e=null,pe=null,ge=0,Re=0,Et=null,zl=!1,Gn=!1,Hs=!1,cl=0,je=0,Cl=0,sn=0,Bs=0,Mt=0,Vn=0,Ka=null,dt=null,Ls=!1,qs=0,hi=1/0,mi=null,Ml=null,Pe=0,Dl=null,Xn=null,Qn=0,ks=0,Ys=null,$d=null,Ja=0,Gs=null;function Rt(){if((Ee&2)!==0&&ge!==0)return ge&-ge;if(j.T!==null){var e=Mn;return e!==0?e:$s()}return co()}function Fd(){Mt===0&&(Mt=(ge&536870912)===0||Se?uo():536870912);var e=Ct.current;return e!==null&&(e.flags|=32),Mt}function Tt(e,t,l){(e===_e&&(Re===2||Re===9)||e.cancelPendingCommit!==null)&&(Zn(e,0),Ul(e,ge,Mt,!1)),da(e,l),((Ee&2)===0||e!==_e)&&(e===_e&&((Ee&2)===0&&(sn|=l),je===4&&Ul(e,ge,Mt,!1)),Kt(e))}function Wd(e,t,l){if((Ee&6)!==0)throw Error(s(327));var n=!l&&(t&124)===0&&(t&e.expiredLanes)===0||fa(e,t),u=n?g0(e,t):Qs(e,t,!0),r=n;do{if(u===0){Gn&&!n&&Ul(e,t,0,!1);break}else{if(l=e.current.alternate,r&&!p0(l)){u=Qs(e,t,!1),r=!1;continue}if(u===2){if(r=t,e.errorRecoveryDisabledLanes&r)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var p=e;u=Ka;var b=p.current.memoizedState.isDehydrated;if(b&&(Zn(p,d).flags|=256),d=Qs(p,d,!1),d!==2){if(Hs&&!b){p.errorRecoveryDisabledLanes|=r,sn|=r,u=4;break e}r=dt,dt=u,r!==null&&(dt===null?dt=r:dt.push.apply(dt,r))}u=d}if(r=!1,u!==2)continue}}if(u===1){Zn(e,0),Ul(e,t,0,!0);break}e:{switch(n=e,r=u,r){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:Ul(n,t,Mt,!zl);break e;case 2:dt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(u=qs+300-Gt(),10<u)){if(Ul(n,t,Mt,!zl),Au(n,0,!0)!==0)break e;n.timeoutHandle=wh(Pd.bind(null,n,l,dt,mi,Ls,t,Mt,sn,Vn,zl,r,2,-0,0),u);break e}Pd(n,l,dt,mi,Ls,t,Mt,sn,Vn,zl,r,0,-0,0)}}break}while(!0);Kt(e)}function Pd(e,t,l,n,u,r,d,p,b,N,H,q,_,C){if(e.timeoutHandle=-1,q=t.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(tu={stylesheets:null,count:0,unsuspend:F0},Qd(t),q=P0(),q!==null)){e.cancelPendingCommit=q(uh.bind(null,e,t,r,l,n,u,d,p,b,H,1,_,C)),Ul(e,r,d,!N);return}uh(e,t,r,l,n,u,d,p,b)}function p0(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var n=0;n<l.length;n++){var u=l[n],r=u.getSnapshot;u=u.value;try{if(!vt(r(),u))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ul(e,t,l,n){t&=~Bs,t&=~sn,e.suspendedLanes|=t,e.pingedLanes&=~t,n&&(e.warmLanes|=t),n=e.expirationTimes;for(var u=t;0<u;){var r=31-gt(u),d=1<<r;n[r]=-1,u&=~d}l!==0&&ro(e,l,t)}function pi(){return(Ee&6)===0?($a(0),!1):!0}function Vs(){if(pe!==null){if(Re===0)var e=pe.return;else e=pe,tl=tn=null,is(e),Ln=null,qa=0,e=pe;for(;e!==null;)Cd(e.alternate,e),e=e.return;pe=null}}function Zn(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,D0(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Vs(),_e=e,pe=l=Pt(e.current,null),ge=t,Re=0,Et=null,zl=!1,Gn=fa(e,t),Hs=!1,Vn=Mt=Bs=sn=Cl=je=0,dt=Ka=null,Ls=!1,(t&8)!==0&&(t|=t&32);var n=e.entangledLanes;if(n!==0)for(e=e.entanglements,n&=t;0<n;){var u=31-gt(n),r=1<<u;t|=e[u],n&=~r}return cl=t,Bu(),l}function Id(e,t){oe=null,j.H=li,t===za||t===Zu?(t=yf(),Re=3):t===hf?(t=yf(),Re=4):Re=t===gd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Et=t,pe===null&&(je=1,ri(e,Ot(t,e.current)))}function eh(){var e=j.H;return j.H=li,e===null?li:e}function th(){var e=j.A;return j.A=h0,e}function Xs(){je=4,zl||(ge&4194048)!==ge&&Ct.current!==null||(Gn=!0),(Cl&134217727)===0&&(sn&134217727)===0||_e===null||Ul(_e,ge,Mt,!1)}function Qs(e,t,l){var n=Ee;Ee|=2;var u=eh(),r=th();(_e!==e||ge!==t)&&(mi=null,Zn(e,t)),t=!1;var d=je;e:do try{if(Re!==0&&pe!==null){var p=pe,b=Et;switch(Re){case 8:Vs(),d=6;break e;case 3:case 2:case 9:case 6:Ct.current===null&&(t=!0);var N=Re;if(Re=0,Et=null,Kn(e,p,b,N),l&&Gn){d=0;break e}break;default:N=Re,Re=0,Et=null,Kn(e,p,b,N)}}y0(),d=je;break}catch(H){Id(e,H)}while(!0);return t&&e.shellSuspendCounter++,tl=tn=null,Ee=n,j.H=u,j.A=r,pe===null&&(_e=null,ge=0,Bu()),d}function y0(){for(;pe!==null;)lh(pe)}function g0(e,t){var l=Ee;Ee|=2;var n=eh(),u=th();_e!==e||ge!==t?(mi=null,hi=Gt()+500,Zn(e,t)):Gn=fa(e,t);e:do try{if(Re!==0&&pe!==null){t=pe;var r=Et;t:switch(Re){case 1:Re=0,Et=null,Kn(e,t,r,1);break;case 2:case 9:if(mf(r)){Re=0,Et=null,nh(t);break}t=function(){Re!==2&&Re!==9||_e!==e||(Re=7),Kt(e)},r.then(t,t);break e;case 3:Re=7;break e;case 4:Re=5;break e;case 7:mf(r)?(Re=0,Et=null,nh(t)):(Re=0,Et=null,Kn(e,t,r,7));break;case 5:var d=null;switch(pe.tag){case 26:d=pe.memoizedState;case 5:case 27:var p=pe;if(!d||Lh(d)){Re=0,Et=null;var b=p.sibling;if(b!==null)pe=b;else{var N=p.return;N!==null?(pe=N,yi(N)):pe=null}break t}}Re=0,Et=null,Kn(e,t,r,5);break;case 6:Re=0,Et=null,Kn(e,t,r,6);break;case 8:Vs(),je=6;break e;default:throw Error(s(462))}}v0();break}catch(H){Id(e,H)}while(!0);return tl=tn=null,j.H=n,j.A=u,Ee=l,pe!==null?0:(_e=null,ge=0,Bu(),je)}function v0(){for(;pe!==null&&!kp();)lh(pe)}function lh(e){var t=_d(e.alternate,e,cl);e.memoizedProps=e.pendingProps,t===null?yi(e):pe=t}function nh(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Rd(l,t,t.pendingProps,t.type,void 0,ge);break;case 11:t=Rd(l,t,t.pendingProps,t.type.render,t.ref,ge);break;case 5:is(t);default:Cd(l,t),t=pe=nf(t,cl),t=_d(l,t,cl)}e.memoizedProps=e.pendingProps,t===null?yi(e):pe=t}function Kn(e,t,l,n){tl=tn=null,is(t),Ln=null,qa=0;var u=t.return;try{if(r0(e,u,t,l,ge)){je=1,ri(e,Ot(l,e.current)),pe=null;return}}catch(r){if(u!==null)throw pe=u,r;je=1,ri(e,Ot(l,e.current)),pe=null;return}t.flags&32768?(Se||n===1?e=!0:Gn||(ge&536870912)!==0?e=!1:(zl=e=!0,(n===2||n===9||n===3||n===6)&&(n=Ct.current,n!==null&&n.tag===13&&(n.flags|=16384))),ah(t,e)):yi(t)}function yi(e){var t=e;do{if((t.flags&32768)!==0){ah(t,zl);return}e=t.return;var l=c0(t.alternate,t,cl);if(l!==null){pe=l;return}if(t=t.sibling,t!==null){pe=t;return}pe=t=e}while(t!==null);je===0&&(je=5)}function ah(e,t){do{var l=o0(e.alternate,e);if(l!==null){l.flags&=32767,pe=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){pe=e;return}pe=e=l}while(e!==null);je=6,pe=null}function uh(e,t,l,n,u,r,d,p,b){e.cancelPendingCommit=null;do gi();while(Pe!==0);if((Ee&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(r=t.lanes|t.childLanes,r|=jr,Fp(e,l,r,d,p,b),e===_e&&(pe=_e=null,ge=0),Xn=t,Dl=e,Qn=l,ks=r,Ys=u,$d=n,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,E0(Eu,function(){return oh(),null})):(e.callbackNode=null,e.callbackPriority=0),n=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||n){n=j.T,j.T=null,u=Z.p,Z.p=2,d=Ee,Ee|=4;try{f0(e,t,l)}finally{Ee=d,Z.p=u,j.T=n}}Pe=1,ih(),rh(),sh()}}function ih(){if(Pe===1){Pe=0;var e=Dl,t=Xn,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=j.T,j.T=null;var n=Z.p;Z.p=2;var u=Ee;Ee|=4;try{Gd(t,e);var r=nc,d=Ko(e.containerInfo),p=r.focusedElem,b=r.selectionRange;if(d!==p&&p&&p.ownerDocument&&Zo(p.ownerDocument.documentElement,p)){if(b!==null&&zr(p)){var N=b.start,H=b.end;if(H===void 0&&(H=N),"selectionStart"in p)p.selectionStart=N,p.selectionEnd=Math.min(H,p.value.length);else{var q=p.ownerDocument||document,_=q&&q.defaultView||window;if(_.getSelection){var C=_.getSelection(),ue=p.textContent.length,ne=Math.min(b.start,ue),we=b.end===void 0?ne:Math.min(b.end,ue);!C.extend&&ne>we&&(d=we,we=ne,ne=d);var T=Qo(p,ne),x=Qo(p,we);if(T&&x&&(C.rangeCount!==1||C.anchorNode!==T.node||C.anchorOffset!==T.offset||C.focusNode!==x.node||C.focusOffset!==x.offset)){var w=q.createRange();w.setStart(T.node,T.offset),C.removeAllRanges(),ne>we?(C.addRange(w),C.extend(x.node,x.offset)):(w.setEnd(x.node,x.offset),C.addRange(w))}}}}for(q=[],C=p;C=C.parentNode;)C.nodeType===1&&q.push({element:C,left:C.scrollLeft,top:C.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<q.length;p++){var L=q[p];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}_i=!!lc,nc=lc=null}finally{Ee=u,Z.p=n,j.T=l}}e.current=t,Pe=2}}function rh(){if(Pe===2){Pe=0;var e=Dl,t=Xn,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=j.T,j.T=null;var n=Z.p;Z.p=2;var u=Ee;Ee|=4;try{Ld(e,t.alternate,t)}finally{Ee=u,Z.p=n,j.T=l}}Pe=3}}function sh(){if(Pe===4||Pe===3){Pe=0,Yp();var e=Dl,t=Xn,l=Qn,n=$d;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Pe=5:(Pe=0,Xn=Dl=null,ch(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(Ml=null),cr(l),t=t.stateNode,yt&&typeof yt.onCommitFiberRoot=="function")try{yt.onCommitFiberRoot(oa,t,void 0,(t.current.flags&128)===128)}catch{}if(n!==null){t=j.T,u=Z.p,Z.p=2,j.T=null;try{for(var r=e.onRecoverableError,d=0;d<n.length;d++){var p=n[d];r(p.value,{componentStack:p.stack})}}finally{j.T=t,Z.p=u}}(Qn&3)!==0&&gi(),Kt(e),u=e.pendingLanes,(l&4194090)!==0&&(u&42)!==0?e===Gs?Ja++:(Ja=0,Gs=e):Ja=0,$a(0)}}function ch(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Na(t)))}function gi(e){return ih(),rh(),sh(),oh()}function oh(){if(Pe!==5)return!1;var e=Dl,t=ks;ks=0;var l=cr(Qn),n=j.T,u=Z.p;try{Z.p=32>l?32:l,j.T=null,l=Ys,Ys=null;var r=Dl,d=Qn;if(Pe=0,Xn=Dl=null,Qn=0,(Ee&6)!==0)throw Error(s(331));var p=Ee;if(Ee|=4,Kd(r.current),Xd(r,r.current,d,l),Ee=p,$a(0,!1),yt&&typeof yt.onPostCommitFiberRoot=="function")try{yt.onPostCommitFiberRoot(oa,r)}catch{}return!0}finally{Z.p=u,j.T=n,ch(e,t)}}function fh(e,t,l){t=Ot(l,t),t=Ss(e.stateNode,t,2),e=Rl(e,t,2),e!==null&&(da(e,2),Kt(e))}function Ne(e,t,l){if(e.tag===3)fh(e,e,l);else for(;t!==null;){if(t.tag===3){fh(t,e,l);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Ml===null||!Ml.has(n))){e=Ot(l,e),l=pd(2),n=Rl(t,l,2),n!==null&&(yd(l,n,t,e),da(n,2),Kt(n));break}}t=t.return}}function Zs(e,t,l){var n=e.pingCache;if(n===null){n=e.pingCache=new m0;var u=new Set;n.set(t,u)}else u=n.get(t),u===void 0&&(u=new Set,n.set(t,u));u.has(l)||(Hs=!0,u.add(l),e=b0.bind(null,e,t,l),t.then(e,e))}function b0(e,t,l){var n=e.pingCache;n!==null&&n.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,_e===e&&(ge&l)===l&&(je===4||je===3&&(ge&62914560)===ge&&300>Gt()-qs?(Ee&2)===0&&Zn(e,0):Bs|=l,Vn===ge&&(Vn=0)),Kt(e)}function dh(e,t){t===0&&(t=io()),e=Nn(e,t),e!==null&&(da(e,t),Kt(e))}function S0(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),dh(e,l)}function x0(e,t){var l=0;switch(e.tag){case 13:var n=e.stateNode,u=e.memoizedState;u!==null&&(l=u.retryLane);break;case 19:n=e.stateNode;break;case 22:n=e.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(t),dh(e,l)}function E0(e,t){return ur(e,t)}var vi=null,Jn=null,Ks=!1,bi=!1,Js=!1,cn=0;function Kt(e){e!==Jn&&e.next===null&&(Jn===null?vi=Jn=e:Jn=Jn.next=e),bi=!0,Ks||(Ks=!0,T0())}function $a(e,t){if(!Js&&bi){Js=!0;do for(var l=!1,n=vi;n!==null;){if(e!==0){var u=n.pendingLanes;if(u===0)var r=0;else{var d=n.suspendedLanes,p=n.pingedLanes;r=(1<<31-gt(42|e)+1)-1,r&=u&~(d&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(l=!0,yh(n,r))}else r=ge,r=Au(n,n===_e?r:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(r&3)===0||fa(n,r)||(l=!0,yh(n,r));n=n.next}while(l);Js=!1}}function R0(){hh()}function hh(){bi=Ks=!1;var e=0;cn!==0&&(M0()&&(e=cn),cn=0);for(var t=Gt(),l=null,n=vi;n!==null;){var u=n.next,r=mh(n,t);r===0?(n.next=null,l===null?vi=u:l.next=u,u===null&&(Jn=l)):(l=n,(e!==0||(r&3)!==0)&&(bi=!0)),n=u}$a(e)}function mh(e,t){for(var l=e.suspendedLanes,n=e.pingedLanes,u=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var d=31-gt(r),p=1<<d,b=u[d];b===-1?((p&l)===0||(p&n)!==0)&&(u[d]=$p(p,t)):b<=t&&(e.expiredLanes|=p),r&=~p}if(t=_e,l=ge,l=Au(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n=e.callbackNode,l===0||e===t&&(Re===2||Re===9)||e.cancelPendingCommit!==null)return n!==null&&n!==null&&ir(n),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||fa(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(n!==null&&ir(n),cr(l)){case 2:case 8:l=no;break;case 32:l=Eu;break;case 268435456:l=ao;break;default:l=Eu}return n=ph.bind(null,e),l=ur(l,n),e.callbackPriority=t,e.callbackNode=l,t}return n!==null&&n!==null&&ir(n),e.callbackPriority=2,e.callbackNode=null,2}function ph(e,t){if(Pe!==0&&Pe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(gi()&&e.callbackNode!==l)return null;var n=ge;return n=Au(e,e===_e?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n===0?null:(Wd(e,n,t),mh(e,Gt()),e.callbackNode!=null&&e.callbackNode===l?ph.bind(null,e):null)}function yh(e,t){if(gi())return null;Wd(e,t,!0)}function T0(){U0(function(){(Ee&6)!==0?ur(lo,R0):hh()})}function $s(){return cn===0&&(cn=uo()),cn}function gh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:zu(""+e)}function vh(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function A0(e,t,l,n,u){if(t==="submit"&&l&&l.stateNode===u){var r=gh((u[st]||null).action),d=n.submitter;d&&(t=(t=d[st]||null)?gh(t.formAction):d.getAttribute("formAction"),t!==null&&(r=t,d=null));var p=new Uu("action","action",null,n,u);e.push({event:p,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(cn!==0){var b=d?vh(u,d):new FormData(u);ps(l,{pending:!0,data:b,method:u.method,action:r},null,b)}}else typeof r=="function"&&(p.preventDefault(),b=d?vh(u,d):new FormData(u),ps(l,{pending:!0,data:b,method:u.method,action:r},r,b))},currentTarget:u}]})}}for(var Fs=0;Fs<Ur.length;Fs++){var Ws=Ur[Fs],w0=Ws.toLowerCase(),O0=Ws[0].toUpperCase()+Ws.slice(1);jt(w0,"on"+O0)}jt(Fo,"onAnimationEnd"),jt(Wo,"onAnimationIteration"),jt(Po,"onAnimationStart"),jt("dblclick","onDoubleClick"),jt("focusin","onFocus"),jt("focusout","onBlur"),jt(Xy,"onTransitionRun"),jt(Qy,"onTransitionStart"),jt(Zy,"onTransitionCancel"),jt(Io,"onTransitionEnd"),vn("onMouseEnter",["mouseout","mouseover"]),vn("onMouseLeave",["mouseout","mouseover"]),vn("onPointerEnter",["pointerout","pointerover"]),vn("onPointerLeave",["pointerout","pointerover"]),Zl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Zl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Zl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Zl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Zl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Zl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),N0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Fa));function bh(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var n=e[l],u=n.event;n=n.listeners;e:{var r=void 0;if(t)for(var d=n.length-1;0<=d;d--){var p=n[d],b=p.instance,N=p.currentTarget;if(p=p.listener,b!==r&&u.isPropagationStopped())break e;r=p,u.currentTarget=N;try{r(u)}catch(H){ii(H)}u.currentTarget=null,r=b}else for(d=0;d<n.length;d++){if(p=n[d],b=p.instance,N=p.currentTarget,p=p.listener,b!==r&&u.isPropagationStopped())break e;r=p,u.currentTarget=N;try{r(u)}catch(H){ii(H)}u.currentTarget=null,r=b}}}}function ye(e,t){var l=t[or];l===void 0&&(l=t[or]=new Set);var n=e+"__bubble";l.has(n)||(Sh(t,e,2,!1),l.add(n))}function Ps(e,t,l){var n=0;t&&(n|=4),Sh(l,e,n,t)}var Si="_reactListening"+Math.random().toString(36).slice(2);function Is(e){if(!e[Si]){e[Si]=!0,fo.forEach(function(l){l!=="selectionchange"&&(N0.has(l)||Ps(l,!1,e),Ps(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Si]||(t[Si]=!0,Ps("selectionchange",!1,t))}}function Sh(e,t,l,n){switch(Xh(t)){case 2:var u=tg;break;case 8:u=lg;break;default:u=hc}l=u.bind(null,t,l,e),u=void 0,!xr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),n?u!==void 0?e.addEventListener(t,l,{capture:!0,passive:u}):e.addEventListener(t,l,!0):u!==void 0?e.addEventListener(t,l,{passive:u}):e.addEventListener(t,l,!1)}function ec(e,t,l,n,u){var r=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var d=n.tag;if(d===3||d===4){var p=n.stateNode.containerInfo;if(p===u)break;if(d===4)for(d=n.return;d!==null;){var b=d.tag;if((b===3||b===4)&&d.stateNode.containerInfo===u)return;d=d.return}for(;p!==null;){if(d=pn(p),d===null)return;if(b=d.tag,b===5||b===6||b===26||b===27){n=r=d;continue e}p=p.parentNode}}n=n.return}wo(function(){var N=r,H=br(l),q=[];e:{var _=ef.get(e);if(_!==void 0){var C=Uu,ue=e;switch(e){case"keypress":if(Mu(l)===0)break e;case"keydown":case"keyup":C=Ey;break;case"focusin":ue="focus",C=Ar;break;case"focusout":ue="blur",C=Ar;break;case"beforeblur":case"afterblur":C=Ar;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":C=_o;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":C=oy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":C=Ay;break;case Fo:case Wo:case Po:C=hy;break;case Io:C=Oy;break;case"scroll":case"scrollend":C=sy;break;case"wheel":C=_y;break;case"copy":case"cut":case"paste":C=py;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":C=Co;break;case"toggle":case"beforetoggle":C=Cy}var ne=(t&4)!==0,we=!ne&&(e==="scroll"||e==="scrollend"),T=ne?_!==null?_+"Capture":null:_;ne=[];for(var x=N,w;x!==null;){var L=x;if(w=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||w===null||T===null||(L=pa(x,T),L!=null&&ne.push(Wa(x,L,w))),we)break;x=x.return}0<ne.length&&(_=new C(_,ue,null,l,H),q.push({event:_,listeners:ne}))}}if((t&7)===0){e:{if(_=e==="mouseover"||e==="pointerover",C=e==="mouseout"||e==="pointerout",_&&l!==vr&&(ue=l.relatedTarget||l.fromElement)&&(pn(ue)||ue[mn]))break e;if((C||_)&&(_=H.window===H?H:(_=H.ownerDocument)?_.defaultView||_.parentWindow:window,C?(ue=l.relatedTarget||l.toElement,C=N,ue=ue?pn(ue):null,ue!==null&&(we=f(ue),ne=ue.tag,ue!==we||ne!==5&&ne!==27&&ne!==6)&&(ue=null)):(C=null,ue=N),C!==ue)){if(ne=_o,L="onMouseLeave",T="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(ne=Co,L="onPointerLeave",T="onPointerEnter",x="pointer"),we=C==null?_:ma(C),w=ue==null?_:ma(ue),_=new ne(L,x+"leave",C,l,H),_.target=we,_.relatedTarget=w,L=null,pn(H)===N&&(ne=new ne(T,x+"enter",ue,l,H),ne.target=w,ne.relatedTarget=we,L=ne),we=L,C&&ue)t:{for(ne=C,T=ue,x=0,w=ne;w;w=$n(w))x++;for(w=0,L=T;L;L=$n(L))w++;for(;0<x-w;)ne=$n(ne),x--;for(;0<w-x;)T=$n(T),w--;for(;x--;){if(ne===T||T!==null&&ne===T.alternate)break t;ne=$n(ne),T=$n(T)}ne=null}else ne=null;C!==null&&xh(q,_,C,ne,!1),ue!==null&&we!==null&&xh(q,we,ue,ne,!0)}}e:{if(_=N?ma(N):window,C=_.nodeName&&_.nodeName.toLowerCase(),C==="select"||C==="input"&&_.type==="file")var P=qo;else if(Bo(_))if(ko)P=Yy;else{P=qy;var de=Ly}else C=_.nodeName,!C||C.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?N&&gr(N.elementType)&&(P=qo):P=ky;if(P&&(P=P(e,N))){Lo(q,P,l,H);break e}de&&de(e,_,N),e==="focusout"&&N&&_.type==="number"&&N.memoizedProps.value!=null&&yr(_,"number",_.value)}switch(de=N?ma(N):window,e){case"focusin":(Bo(de)||de.contentEditable==="true")&&(An=de,Cr=N,Ra=null);break;case"focusout":Ra=Cr=An=null;break;case"mousedown":Mr=!0;break;case"contextmenu":case"mouseup":case"dragend":Mr=!1,Jo(q,l,H);break;case"selectionchange":if(Vy)break;case"keydown":case"keyup":Jo(q,l,H)}var te;if(Or)e:{switch(e){case"compositionstart":var ae="onCompositionStart";break e;case"compositionend":ae="onCompositionEnd";break e;case"compositionupdate":ae="onCompositionUpdate";break e}ae=void 0}else Tn?jo(e,l)&&(ae="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(ae="onCompositionStart");ae&&(Mo&&l.locale!=="ko"&&(Tn||ae!=="onCompositionStart"?ae==="onCompositionEnd"&&Tn&&(te=Oo()):(bl=H,Er="value"in bl?bl.value:bl.textContent,Tn=!0)),de=xi(N,ae),0<de.length&&(ae=new zo(ae,e,null,l,H),q.push({event:ae,listeners:de}),te?ae.data=te:(te=Ho(l),te!==null&&(ae.data=te)))),(te=Dy?Uy(e,l):jy(e,l))&&(ae=xi(N,"onBeforeInput"),0<ae.length&&(de=new zo("onBeforeInput","beforeinput",null,l,H),q.push({event:de,listeners:ae}),de.data=te)),A0(q,e,N,l,H)}bh(q,t)})}function Wa(e,t,l){return{instance:e,listener:t,currentTarget:l}}function xi(e,t){for(var l=t+"Capture",n=[];e!==null;){var u=e,r=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||r===null||(u=pa(e,l),u!=null&&n.unshift(Wa(e,u,r)),u=pa(e,t),u!=null&&n.push(Wa(e,u,r))),e.tag===3)return n;e=e.return}return[]}function $n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function xh(e,t,l,n,u){for(var r=t._reactName,d=[];l!==null&&l!==n;){var p=l,b=p.alternate,N=p.stateNode;if(p=p.tag,b!==null&&b===n)break;p!==5&&p!==26&&p!==27||N===null||(b=N,u?(N=pa(l,r),N!=null&&d.unshift(Wa(l,N,b))):u||(N=pa(l,r),N!=null&&d.push(Wa(l,N,b)))),l=l.return}d.length!==0&&e.push({event:t,listeners:d})}var _0=/\r\n?/g,z0=/\u0000|\uFFFD/g;function Eh(e){return(typeof e=="string"?e:""+e).replace(_0,`
`).replace(z0,"")}function Rh(e,t){return t=Eh(t),Eh(e)===t}function Ei(){}function Ae(e,t,l,n,u,r){switch(l){case"children":typeof n=="string"?t==="body"||t==="textarea"&&n===""||xn(e,n):(typeof n=="number"||typeof n=="bigint")&&t!=="body"&&xn(e,""+n);break;case"className":Ou(e,"class",n);break;case"tabIndex":Ou(e,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":Ou(e,l,n);break;case"style":To(e,n,r);break;case"data":if(t!=="object"){Ou(e,"data",n);break}case"src":case"href":if(n===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(l);break}n=zu(""+n),e.setAttribute(l,n);break;case"action":case"formAction":if(typeof n=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(l==="formAction"?(t!=="input"&&Ae(e,t,"name",u.name,u,null),Ae(e,t,"formEncType",u.formEncType,u,null),Ae(e,t,"formMethod",u.formMethod,u,null),Ae(e,t,"formTarget",u.formTarget,u,null)):(Ae(e,t,"encType",u.encType,u,null),Ae(e,t,"method",u.method,u,null),Ae(e,t,"target",u.target,u,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(l);break}n=zu(""+n),e.setAttribute(l,n);break;case"onClick":n!=null&&(e.onclick=Ei);break;case"onScroll":n!=null&&ye("scroll",e);break;case"onScrollEnd":n!=null&&ye("scrollend",e);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"multiple":e.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":e.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){e.removeAttribute("xlink:href");break}l=zu(""+n),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(l,""+n):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":n===!0?e.setAttribute(l,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(l,n):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?e.setAttribute(l,n):e.removeAttribute(l);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?e.removeAttribute(l):e.setAttribute(l,n);break;case"popover":ye("beforetoggle",e),ye("toggle",e),wu(e,"popover",n);break;case"xlinkActuate":Ft(e,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":Ft(e,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":Ft(e,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":Ft(e,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":Ft(e,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":Ft(e,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":Ft(e,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":wu(e,"is",n);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=iy.get(l)||l,wu(e,l,n))}}function tc(e,t,l,n,u,r){switch(l){case"style":To(e,n,r);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(u.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"children":typeof n=="string"?xn(e,n):(typeof n=="number"||typeof n=="bigint")&&xn(e,""+n);break;case"onScroll":n!=null&&ye("scroll",e);break;case"onScrollEnd":n!=null&&ye("scrollend",e);break;case"onClick":n!=null&&(e.onclick=Ei);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ho.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(u=l.endsWith("Capture"),t=l.slice(2,u?l.length-7:void 0),r=e[st]||null,r=r!=null?r[l]:null,typeof r=="function"&&e.removeEventListener(t,r,u),typeof n=="function")){typeof r!="function"&&r!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,n,u);break e}l in e?e[l]=n:n===!0?e.setAttribute(l,""):wu(e,l,n)}}}function Ie(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ye("error",e),ye("load",e);var n=!1,u=!1,r;for(r in l)if(l.hasOwnProperty(r)){var d=l[r];if(d!=null)switch(r){case"src":n=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ae(e,t,r,d,l,null)}}u&&Ae(e,t,"srcSet",l.srcSet,l,null),n&&Ae(e,t,"src",l.src,l,null);return;case"input":ye("invalid",e);var p=r=d=u=null,b=null,N=null;for(n in l)if(l.hasOwnProperty(n)){var H=l[n];if(H!=null)switch(n){case"name":u=H;break;case"type":d=H;break;case"checked":b=H;break;case"defaultChecked":N=H;break;case"value":r=H;break;case"defaultValue":p=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(s(137,t));break;default:Ae(e,t,n,H,l,null)}}So(e,r,p,b,N,d,u,!1),Nu(e);return;case"select":ye("invalid",e),n=d=r=null;for(u in l)if(l.hasOwnProperty(u)&&(p=l[u],p!=null))switch(u){case"value":r=p;break;case"defaultValue":d=p;break;case"multiple":n=p;default:Ae(e,t,u,p,l,null)}t=r,l=d,e.multiple=!!n,t!=null?Sn(e,!!n,t,!1):l!=null&&Sn(e,!!n,l,!0);return;case"textarea":ye("invalid",e),r=u=n=null;for(d in l)if(l.hasOwnProperty(d)&&(p=l[d],p!=null))switch(d){case"value":n=p;break;case"defaultValue":u=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(91));break;default:Ae(e,t,d,p,l,null)}Eo(e,n,u,r),Nu(e);return;case"option":for(b in l)if(l.hasOwnProperty(b)&&(n=l[b],n!=null))switch(b){case"selected":e.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:Ae(e,t,b,n,l,null)}return;case"dialog":ye("beforetoggle",e),ye("toggle",e),ye("cancel",e),ye("close",e);break;case"iframe":case"object":ye("load",e);break;case"video":case"audio":for(n=0;n<Fa.length;n++)ye(Fa[n],e);break;case"image":ye("error",e),ye("load",e);break;case"details":ye("toggle",e);break;case"embed":case"source":case"link":ye("error",e),ye("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in l)if(l.hasOwnProperty(N)&&(n=l[N],n!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ae(e,t,N,n,l,null)}return;default:if(gr(t)){for(H in l)l.hasOwnProperty(H)&&(n=l[H],n!==void 0&&tc(e,t,H,n,l,void 0));return}}for(p in l)l.hasOwnProperty(p)&&(n=l[p],n!=null&&Ae(e,t,p,n,l,null))}function C0(e,t,l,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,r=null,d=null,p=null,b=null,N=null,H=null;for(C in l){var q=l[C];if(l.hasOwnProperty(C)&&q!=null)switch(C){case"checked":break;case"value":break;case"defaultValue":b=q;default:n.hasOwnProperty(C)||Ae(e,t,C,null,n,q)}}for(var _ in n){var C=n[_];if(q=l[_],n.hasOwnProperty(_)&&(C!=null||q!=null))switch(_){case"type":r=C;break;case"name":u=C;break;case"checked":N=C;break;case"defaultChecked":H=C;break;case"value":d=C;break;case"defaultValue":p=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(s(137,t));break;default:C!==q&&Ae(e,t,_,C,n,q)}}pr(e,d,p,b,N,H,r,u);return;case"select":C=d=p=_=null;for(r in l)if(b=l[r],l.hasOwnProperty(r)&&b!=null)switch(r){case"value":break;case"multiple":C=b;default:n.hasOwnProperty(r)||Ae(e,t,r,null,n,b)}for(u in n)if(r=n[u],b=l[u],n.hasOwnProperty(u)&&(r!=null||b!=null))switch(u){case"value":_=r;break;case"defaultValue":p=r;break;case"multiple":d=r;default:r!==b&&Ae(e,t,u,r,n,b)}t=p,l=d,n=C,_!=null?Sn(e,!!l,_,!1):!!n!=!!l&&(t!=null?Sn(e,!!l,t,!0):Sn(e,!!l,l?[]:"",!1));return;case"textarea":C=_=null;for(p in l)if(u=l[p],l.hasOwnProperty(p)&&u!=null&&!n.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Ae(e,t,p,null,n,u)}for(d in n)if(u=n[d],r=l[d],n.hasOwnProperty(d)&&(u!=null||r!=null))switch(d){case"value":_=u;break;case"defaultValue":C=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(s(91));break;default:u!==r&&Ae(e,t,d,u,n,r)}xo(e,_,C);return;case"option":for(var ue in l)if(_=l[ue],l.hasOwnProperty(ue)&&_!=null&&!n.hasOwnProperty(ue))switch(ue){case"selected":e.selected=!1;break;default:Ae(e,t,ue,null,n,_)}for(b in n)if(_=n[b],C=l[b],n.hasOwnProperty(b)&&_!==C&&(_!=null||C!=null))switch(b){case"selected":e.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:Ae(e,t,b,_,n,C)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ne in l)_=l[ne],l.hasOwnProperty(ne)&&_!=null&&!n.hasOwnProperty(ne)&&Ae(e,t,ne,null,n,_);for(N in n)if(_=n[N],C=l[N],n.hasOwnProperty(N)&&_!==C&&(_!=null||C!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(s(137,t));break;default:Ae(e,t,N,_,n,C)}return;default:if(gr(t)){for(var we in l)_=l[we],l.hasOwnProperty(we)&&_!==void 0&&!n.hasOwnProperty(we)&&tc(e,t,we,void 0,n,_);for(H in n)_=n[H],C=l[H],!n.hasOwnProperty(H)||_===C||_===void 0&&C===void 0||tc(e,t,H,_,n,C);return}}for(var T in l)_=l[T],l.hasOwnProperty(T)&&_!=null&&!n.hasOwnProperty(T)&&Ae(e,t,T,null,n,_);for(q in n)_=n[q],C=l[q],!n.hasOwnProperty(q)||_===C||_==null&&C==null||Ae(e,t,q,_,n,C)}var lc=null,nc=null;function Ri(e){return e.nodeType===9?e:e.ownerDocument}function Th(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ah(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function ac(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var uc=null;function M0(){var e=window.event;return e&&e.type==="popstate"?e===uc?!1:(uc=e,!0):(uc=null,!1)}var wh=typeof setTimeout=="function"?setTimeout:void 0,D0=typeof clearTimeout=="function"?clearTimeout:void 0,Oh=typeof Promise=="function"?Promise:void 0,U0=typeof queueMicrotask=="function"?queueMicrotask:typeof Oh<"u"?function(e){return Oh.resolve(null).then(e).catch(j0)}:wh;function j0(e){setTimeout(function(){throw e})}function jl(e){return e==="head"}function Nh(e,t){var l=t,n=0,u=0;do{var r=l.nextSibling;if(e.removeChild(l),r&&r.nodeType===8)if(l=r.data,l==="/$"){if(0<n&&8>n){l=n;var d=e.ownerDocument;if(l&1&&Pa(d.documentElement),l&2&&Pa(d.body),l&4)for(l=d.head,Pa(l),d=l.firstChild;d;){var p=d.nextSibling,b=d.nodeName;d[ha]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&d.rel.toLowerCase()==="stylesheet"||l.removeChild(d),d=p}}if(u===0){e.removeChild(r),iu(t);return}u--}else l==="$"||l==="$?"||l==="$!"?u++:n=l.charCodeAt(0)-48;else n=0;l=r}while(l);iu(t)}function ic(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":ic(l),fr(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function H0(e,t,l,n){for(;e.nodeType===1;){var u=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!n&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(n){if(!e[ha])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Bt(e.nextSibling),e===null)break}return null}function B0(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Bt(e.nextSibling),e===null))return null;return e}function rc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function L0(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var n=function(){t(),l.removeEventListener("DOMContentLoaded",n)};l.addEventListener("DOMContentLoaded",n),e._reactRetry=n}}function Bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var sc=null;function _h(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function zh(e,t,l){switch(t=Ri(l),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Pa(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);fr(e)}var Dt=new Map,Ch=new Set;function Ti(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ol=Z.d;Z.d={f:q0,r:k0,D:Y0,C:G0,L:V0,m:X0,X:Z0,S:Q0,M:K0};function q0(){var e=ol.f(),t=pi();return e||t}function k0(e){var t=yn(e);t!==null&&t.tag===5&&t.type==="form"?Wf(t):ol.r(e)}var Fn=typeof document>"u"?null:document;function Mh(e,t,l){var n=Fn;if(n&&typeof t=="string"&&t){var u=wt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof l=="string"&&(u+='[crossorigin="'+l+'"]'),Ch.has(u)||(Ch.add(u),e={rel:e,crossOrigin:l,href:t},n.querySelector(u)===null&&(t=n.createElement("link"),Ie(t,"link",e),Ze(t),n.head.appendChild(t)))}}function Y0(e){ol.D(e),Mh("dns-prefetch",e,null)}function G0(e,t){ol.C(e,t),Mh("preconnect",e,t)}function V0(e,t,l){ol.L(e,t,l);var n=Fn;if(n&&e&&t){var u='link[rel="preload"][as="'+wt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(u+='[imagesrcset="'+wt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(u+='[imagesizes="'+wt(l.imageSizes)+'"]')):u+='[href="'+wt(e)+'"]';var r=u;switch(t){case"style":r=Wn(e);break;case"script":r=Pn(e)}Dt.has(r)||(e=v({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),Dt.set(r,e),n.querySelector(u)!==null||t==="style"&&n.querySelector(Ia(r))||t==="script"&&n.querySelector(eu(r))||(t=n.createElement("link"),Ie(t,"link",e),Ze(t),n.head.appendChild(t)))}}function X0(e,t){ol.m(e,t);var l=Fn;if(l&&e){var n=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+wt(n)+'"][href="'+wt(e)+'"]',r=u;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Pn(e)}if(!Dt.has(r)&&(e=v({rel:"modulepreload",href:e},t),Dt.set(r,e),l.querySelector(u)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(eu(r)))return}n=l.createElement("link"),Ie(n,"link",e),Ze(n),l.head.appendChild(n)}}}function Q0(e,t,l){ol.S(e,t,l);var n=Fn;if(n&&e){var u=gn(n).hoistableStyles,r=Wn(e);t=t||"default";var d=u.get(r);if(!d){var p={loading:0,preload:null};if(d=n.querySelector(Ia(r)))p.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},l),(l=Dt.get(r))&&cc(e,l);var b=d=n.createElement("link");Ze(b),Ie(b,"link",e),b._p=new Promise(function(N,H){b.onload=N,b.onerror=H}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,Ai(d,t,n)}d={type:"stylesheet",instance:d,count:1,state:p},u.set(r,d)}}}function Z0(e,t){ol.X(e,t);var l=Fn;if(l&&e){var n=gn(l).hoistableScripts,u=Pn(e),r=n.get(u);r||(r=l.querySelector(eu(u)),r||(e=v({src:e,async:!0},t),(t=Dt.get(u))&&oc(e,t),r=l.createElement("script"),Ze(r),Ie(r,"link",e),l.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},n.set(u,r))}}function K0(e,t){ol.M(e,t);var l=Fn;if(l&&e){var n=gn(l).hoistableScripts,u=Pn(e),r=n.get(u);r||(r=l.querySelector(eu(u)),r||(e=v({src:e,async:!0,type:"module"},t),(t=Dt.get(u))&&oc(e,t),r=l.createElement("script"),Ze(r),Ie(r,"link",e),l.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},n.set(u,r))}}function Dh(e,t,l,n){var u=(u=ie.current)?Ti(u):null;if(!u)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Wn(l.href),l=gn(u).hoistableStyles,n=l.get(t),n||(n={type:"style",instance:null,count:0,state:null},l.set(t,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Wn(l.href);var r=gn(u).hoistableStyles,d=r.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,d),(r=u.querySelector(Ia(e)))&&!r._p&&(d.instance=r,d.state.loading=5),Dt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Dt.set(e,l),r||J0(u,e,l,d.state))),t&&n===null)throw Error(s(528,""));return d}if(t&&n!==null)throw Error(s(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Pn(l),l=gn(u).hoistableScripts,n=l.get(t),n||(n={type:"script",instance:null,count:0,state:null},l.set(t,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function Wn(e){return'href="'+wt(e)+'"'}function Ia(e){return'link[rel="stylesheet"]['+e+"]"}function Uh(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function J0(e,t,l,n){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?n.loading=1:(t=e.createElement("link"),n.preload=t,t.addEventListener("load",function(){return n.loading|=1}),t.addEventListener("error",function(){return n.loading|=2}),Ie(t,"link",l),Ze(t),e.head.appendChild(t))}function Pn(e){return'[src="'+wt(e)+'"]'}function eu(e){return"script[async]"+e}function jh(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var n=e.querySelector('style[data-href~="'+wt(l.href)+'"]');if(n)return t.instance=n,Ze(n),n;var u=v({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return n=(e.ownerDocument||e).createElement("style"),Ze(n),Ie(n,"style",u),Ai(n,l.precedence,e),t.instance=n;case"stylesheet":u=Wn(l.href);var r=e.querySelector(Ia(u));if(r)return t.state.loading|=4,t.instance=r,Ze(r),r;n=Uh(l),(u=Dt.get(u))&&cc(n,u),r=(e.ownerDocument||e).createElement("link"),Ze(r);var d=r;return d._p=new Promise(function(p,b){d.onload=p,d.onerror=b}),Ie(r,"link",n),t.state.loading|=4,Ai(r,l.precedence,e),t.instance=r;case"script":return r=Pn(l.src),(u=e.querySelector(eu(r)))?(t.instance=u,Ze(u),u):(n=l,(u=Dt.get(r))&&(n=v({},l),oc(n,u)),e=e.ownerDocument||e,u=e.createElement("script"),Ze(u),Ie(u,"link",n),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(n=t.instance,t.state.loading|=4,Ai(n,l.precedence,e));return t.instance}function Ai(e,t,l){for(var n=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=n.length?n[n.length-1]:null,r=u,d=0;d<n.length;d++){var p=n[d];if(p.dataset.precedence===t)r=p;else if(r!==u)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function cc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function oc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var wi=null;function Hh(e,t,l){if(wi===null){var n=new Map,u=wi=new Map;u.set(l,n)}else u=wi,n=u.get(l),n||(n=new Map,u.set(l,n));if(n.has(e))return n;for(n.set(e,null),l=l.getElementsByTagName(e),u=0;u<l.length;u++){var r=l[u];if(!(r[ha]||r[et]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var d=r.getAttribute(t)||"";d=e+d;var p=n.get(d);p?p.push(r):n.set(d,[r])}}return n}function Bh(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function $0(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Lh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var tu=null;function F0(){}function W0(e,t,l){if(tu===null)throw Error(s(475));var n=tu;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Wn(l.href),r=e.querySelector(Ia(u));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(n.count++,n=Oi.bind(n),e.then(n,n)),t.state.loading|=4,t.instance=r,Ze(r);return}r=e.ownerDocument||e,l=Uh(l),(u=Dt.get(u))&&cc(l,u),r=r.createElement("link"),Ze(r);var d=r;d._p=new Promise(function(p,b){d.onload=p,d.onerror=b}),Ie(r,"link",l),t.instance=r}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(n.count++,t=Oi.bind(n),e.addEventListener("load",t),e.addEventListener("error",t))}}function P0(){if(tu===null)throw Error(s(475));var e=tu;return e.stylesheets&&e.count===0&&fc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&fc(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Oi(){if(this.count--,this.count===0){if(this.stylesheets)fc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ni=null;function fc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ni=new Map,t.forEach(I0,e),Ni=null,Oi.call(e))}function I0(e,t){if(!(t.state.loading&4)){var l=Ni.get(e);if(l)var n=l.get(null);else{l=new Map,Ni.set(e,l);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<u.length;r++){var d=u[r];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(l.set(d.dataset.precedence,d),n=d)}n&&l.set(null,n)}u=t.instance,d=u.getAttribute("data-precedence"),r=l.get(d)||n,r===n&&l.set(null,u),l.set(d,u),this.count++,n=Oi.bind(this),u.addEventListener("load",n),u.addEventListener("error",n),r?r.parentNode.insertBefore(u,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var lu={$$typeof:Q,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function eg(e,t,l,n,u,r,d,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=rr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=rr(0),this.hiddenUpdates=rr(null),this.identifierPrefix=n,this.onUncaughtError=u,this.onCaughtError=r,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function qh(e,t,l,n,u,r,d,p,b,N,H,q){return e=new eg(e,t,l,d,p,b,N,q),t=1,r===!0&&(t|=24),r=bt(3,null,null,t),e.current=r,r.stateNode=e,t=Zr(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:n,isDehydrated:l,cache:t},Fr(r),e}function kh(e){return e?(e=_n,e):_n}function Yh(e,t,l,n,u,r){u=kh(u),n.context===null?n.context=u:n.pendingContext=u,n=El(t),n.payload={element:l},r=r===void 0?null:r,r!==null&&(n.callback=r),l=Rl(e,n,t),l!==null&&(Tt(l,e,t),Ma(l,e,t))}function Gh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function dc(e,t){Gh(e,t),(e=e.alternate)&&Gh(e,t)}function Vh(e){if(e.tag===13){var t=Nn(e,67108864);t!==null&&Tt(t,e,67108864),dc(e,67108864)}}var _i=!0;function tg(e,t,l,n){var u=j.T;j.T=null;var r=Z.p;try{Z.p=2,hc(e,t,l,n)}finally{Z.p=r,j.T=u}}function lg(e,t,l,n){var u=j.T;j.T=null;var r=Z.p;try{Z.p=8,hc(e,t,l,n)}finally{Z.p=r,j.T=u}}function hc(e,t,l,n){if(_i){var u=mc(n);if(u===null)ec(e,t,n,zi,l),Qh(e,n);else if(ag(u,e,t,l,n))n.stopPropagation();else if(Qh(e,n),t&4&&-1<ng.indexOf(e)){for(;u!==null;){var r=yn(u);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var d=Ql(r.pendingLanes);if(d!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;d;){var b=1<<31-gt(d);p.entanglements[1]|=b,d&=~b}Kt(r),(Ee&6)===0&&(hi=Gt()+500,$a(0))}}break;case 13:p=Nn(r,2),p!==null&&Tt(p,r,2),pi(),dc(r,2)}if(r=mc(n),r===null&&ec(e,t,n,zi,l),r===u)break;u=r}u!==null&&n.stopPropagation()}else ec(e,t,n,null,l)}}function mc(e){return e=br(e),pc(e)}var zi=null;function pc(e){if(zi=null,e=pn(e),e!==null){var t=f(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=h(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return zi=e,null}function Xh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Gp()){case lo:return 2;case no:return 8;case Eu:case Vp:return 32;case ao:return 268435456;default:return 32}default:return 32}}var yc=!1,Hl=null,Bl=null,Ll=null,nu=new Map,au=new Map,ql=[],ng="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Qh(e,t){switch(e){case"focusin":case"focusout":Hl=null;break;case"dragenter":case"dragleave":Bl=null;break;case"mouseover":case"mouseout":Ll=null;break;case"pointerover":case"pointerout":nu.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":au.delete(t.pointerId)}}function uu(e,t,l,n,u,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:l,eventSystemFlags:n,nativeEvent:r,targetContainers:[u]},t!==null&&(t=yn(t),t!==null&&Vh(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function ag(e,t,l,n,u){switch(t){case"focusin":return Hl=uu(Hl,e,t,l,n,u),!0;case"dragenter":return Bl=uu(Bl,e,t,l,n,u),!0;case"mouseover":return Ll=uu(Ll,e,t,l,n,u),!0;case"pointerover":var r=u.pointerId;return nu.set(r,uu(nu.get(r)||null,e,t,l,n,u)),!0;case"gotpointercapture":return r=u.pointerId,au.set(r,uu(au.get(r)||null,e,t,l,n,u)),!0}return!1}function Zh(e){var t=pn(e.target);if(t!==null){var l=f(t);if(l!==null){if(t=l.tag,t===13){if(t=h(l),t!==null){e.blockedOn=t,Wp(e.priority,function(){if(l.tag===13){var n=Rt();n=sr(n);var u=Nn(l,n);u!==null&&Tt(u,l,n),dc(l,n)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=mc(e.nativeEvent);if(l===null){l=e.nativeEvent;var n=new l.constructor(l.type,l);vr=n,l.target.dispatchEvent(n),vr=null}else return t=yn(l),t!==null&&Vh(t),e.blockedOn=l,!1;t.shift()}return!0}function Kh(e,t,l){Ci(e)&&l.delete(t)}function ug(){yc=!1,Hl!==null&&Ci(Hl)&&(Hl=null),Bl!==null&&Ci(Bl)&&(Bl=null),Ll!==null&&Ci(Ll)&&(Ll=null),nu.forEach(Kh),au.forEach(Kh)}function Mi(e,t){e.blockedOn===t&&(e.blockedOn=null,yc||(yc=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,ug)))}var Di=null;function Jh(e){Di!==e&&(Di=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Di===e&&(Di=null);for(var t=0;t<e.length;t+=3){var l=e[t],n=e[t+1],u=e[t+2];if(typeof n!="function"){if(pc(n||l)===null)continue;break}var r=yn(l);r!==null&&(e.splice(t,3),t-=3,ps(r,{pending:!0,data:u,method:l.method,action:n},n,u))}}))}function iu(e){function t(b){return Mi(b,e)}Hl!==null&&Mi(Hl,e),Bl!==null&&Mi(Bl,e),Ll!==null&&Mi(Ll,e),nu.forEach(t),au.forEach(t);for(var l=0;l<ql.length;l++){var n=ql[l];n.blockedOn===e&&(n.blockedOn=null)}for(;0<ql.length&&(l=ql[0],l.blockedOn===null);)Zh(l),l.blockedOn===null&&ql.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(n=0;n<l.length;n+=3){var u=l[n],r=l[n+1],d=u[st]||null;if(typeof r=="function")d||Jh(l);else if(d){var p=null;if(r&&r.hasAttribute("formAction")){if(u=r,d=r[st]||null)p=d.formAction;else if(pc(u)!==null)continue}else p=d.action;typeof p=="function"?l[n+1]=p:(l.splice(n,3),n-=3),Jh(l)}}}function gc(e){this._internalRoot=e}Ui.prototype.render=gc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var l=t.current,n=Rt();Yh(l,n,e,t,null,null)},Ui.prototype.unmount=gc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yh(e.current,2,null,e,null,null),pi(),t[mn]=null}};function Ui(e){this._internalRoot=e}Ui.prototype.unstable_scheduleHydration=function(e){if(e){var t=co();e={blockedOn:null,target:e,priority:t};for(var l=0;l<ql.length&&t!==0&&t<ql[l].priority;l++);ql.splice(l,0,e),l===0&&Zh(e)}};var $h=i.version;if($h!=="19.1.0")throw Error(s(527,$h,"19.1.0"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=y(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var ig={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:j,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ji=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ji.isDisabled&&ji.supportsFiber)try{oa=ji.inject(ig),yt=ji}catch{}}return su.createRoot=function(e,t){if(!o(e))throw Error(s(299));var l=!1,n="",u=fd,r=dd,d=hd,p=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=qh(e,1,!1,null,null,l,n,u,r,d,p,null),e[mn]=t.current,Is(e),new gc(t)},su.hydrateRoot=function(e,t,l){if(!o(e))throw Error(s(299));var n=!1,u="",r=fd,d=dd,p=hd,b=null,N=null;return l!=null&&(l.unstable_strictMode===!0&&(n=!0),l.identifierPrefix!==void 0&&(u=l.identifierPrefix),l.onUncaughtError!==void 0&&(r=l.onUncaughtError),l.onCaughtError!==void 0&&(d=l.onCaughtError),l.onRecoverableError!==void 0&&(p=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(b=l.unstable_transitionCallbacks),l.formState!==void 0&&(N=l.formState)),t=qh(e,1,!0,t,l??null,n,u,r,d,p,b,N),t.context=kh(null),l=t.current,n=Rt(),n=sr(n),u=El(n),u.callback=null,Rl(l,u,n),l=n,t.current.lanes=l,da(t,l),Kt(t),e[mn]=t.current,Is(e),new Ui(t)},su.version="19.1.0",su}var im;function gg(){if(im)return Sc.exports;im=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Sc.exports=yg(),Sc.exports}var vg=gg(),cu={},rm;function bg(){if(rm)return cu;rm=1,Object.defineProperty(cu,"__esModule",{value:!0}),cu.parse=h,cu.serialize=m;const a=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,c=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const O=function(){};return O.prototype=Object.create(null),O})();function h(O,k){const R=new f,z=O.length;if(z<2)return R;const U=(k==null?void 0:k.decode)||v;let B=0;do{const G=O.indexOf("=",B);if(G===-1)break;const Q=O.indexOf(";",B),le=Q===-1?z:Q;if(G>le){B=O.lastIndexOf(";",G-1)+1;continue}const K=g(O,B,G),ce=y(O,G,K),J=O.slice(K,ce);if(R[J]===void 0){let $=g(O,G+1,le),he=y(O,le,$);const Qe=U(O.slice($,he));R[J]=Qe}B=le+1}while(B<z);return R}function g(O,k,R){do{const z=O.charCodeAt(k);if(z!==32&&z!==9)return k}while(++k<R);return R}function y(O,k,R){for(;k>R;){const z=O.charCodeAt(--k);if(z!==32&&z!==9)return k+1}return R}function m(O,k,R){const z=(R==null?void 0:R.encode)||encodeURIComponent;if(!a.test(O))throw new TypeError(`argument name is invalid: ${O}`);const U=z(k);if(!i.test(U))throw new TypeError(`argument val is invalid: ${k}`);let B=O+"="+U;if(!R)return B;if(R.maxAge!==void 0){if(!Number.isInteger(R.maxAge))throw new TypeError(`option maxAge is invalid: ${R.maxAge}`);B+="; Max-Age="+R.maxAge}if(R.domain){if(!c.test(R.domain))throw new TypeError(`option domain is invalid: ${R.domain}`);B+="; Domain="+R.domain}if(R.path){if(!s.test(R.path))throw new TypeError(`option path is invalid: ${R.path}`);B+="; Path="+R.path}if(R.expires){if(!A(R.expires)||!Number.isFinite(R.expires.valueOf()))throw new TypeError(`option expires is invalid: ${R.expires}`);B+="; Expires="+R.expires.toUTCString()}if(R.httpOnly&&(B+="; HttpOnly"),R.secure&&(B+="; Secure"),R.partitioned&&(B+="; Partitioned"),R.priority)switch(typeof R.priority=="string"?R.priority.toLowerCase():void 0){case"low":B+="; Priority=Low";break;case"medium":B+="; Priority=Medium";break;case"high":B+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${R.priority}`)}if(R.sameSite)switch(typeof R.sameSite=="string"?R.sameSite.toLowerCase():R.sameSite){case!0:case"strict":B+="; SameSite=Strict";break;case"lax":B+="; SameSite=Lax";break;case"none":B+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${R.sameSite}`)}return B}function v(O){if(O.indexOf("%")===-1)return O;try{return decodeURIComponent(O)}catch{return O}}function A(O){return o.call(O)==="[object Date]"}return cu}bg();var sm="popstate";function Sg(a={}){function i(s,o){let{pathname:f,search:h,hash:g}=s.location;return Cc("",{pathname:f,search:h,hash:g},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function c(s,o){return typeof o=="string"?o:hu(o)}return Eg(i,c,null,a)}function Ce(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}function Lt(a,i){if(!a){typeof console<"u"&&console.warn(i);try{throw new Error(i)}catch{}}}function xg(){return Math.random().toString(36).substring(2,10)}function cm(a,i){return{usr:a.state,key:a.key,idx:i}}function Cc(a,i,c=null,s){return{pathname:typeof a=="string"?a:a.pathname,search:"",hash:"",...typeof i=="string"?na(i):i,state:c,key:i&&i.key||s||xg()}}function hu({pathname:a="/",search:i="",hash:c=""}){return i&&i!=="?"&&(a+=i.charAt(0)==="?"?i:"?"+i),c&&c!=="#"&&(a+=c.charAt(0)==="#"?c:"#"+c),a}function na(a){let i={};if(a){let c=a.indexOf("#");c>=0&&(i.hash=a.substring(c),a=a.substring(0,c));let s=a.indexOf("?");s>=0&&(i.search=a.substring(s),a=a.substring(0,s)),a&&(i.pathname=a)}return i}function Eg(a,i,c,s={}){let{window:o=document.defaultView,v5Compat:f=!1}=s,h=o.history,g="POP",y=null,m=v();m==null&&(m=0,h.replaceState({...h.state,idx:m},""));function v(){return(h.state||{idx:null}).idx}function A(){g="POP";let U=v(),B=U==null?null:U-m;m=U,y&&y({action:g,location:z.location,delta:B})}function O(U,B){g="PUSH";let G=Cc(z.location,U,B);m=v()+1;let Q=cm(G,m),le=z.createHref(G);try{h.pushState(Q,"",le)}catch(K){if(K instanceof DOMException&&K.name==="DataCloneError")throw K;o.location.assign(le)}f&&y&&y({action:g,location:z.location,delta:1})}function k(U,B){g="REPLACE";let G=Cc(z.location,U,B);m=v();let Q=cm(G,m),le=z.createHref(G);h.replaceState(Q,"",le),f&&y&&y({action:g,location:z.location,delta:0})}function R(U){return Rg(U)}let z={get action(){return g},get location(){return a(o,h)},listen(U){if(y)throw new Error("A history only accepts one active listener");return o.addEventListener(sm,A),y=U,()=>{o.removeEventListener(sm,A),y=null}},createHref(U){return i(o,U)},createURL:R,encodeLocation(U){let B=R(U);return{pathname:B.pathname,search:B.search,hash:B.hash}},push:O,replace:k,go(U){return h.go(U)}};return z}function Rg(a,i=!1){let c="http://localhost";typeof window<"u"&&(c=window.location.origin!=="null"?window.location.origin:window.location.href),Ce(c,"No window.location.(origin|href) available to create URL");let s=typeof a=="string"?a:hu(a);return s=s.replace(/ $/,"%20"),!i&&s.startsWith("//")&&(s=c+s),new URL(s,c)}function qm(a,i,c="/"){return Tg(a,i,c,!1)}function Tg(a,i,c,s){let o=typeof i=="string"?na(i):i,f=hl(o.pathname||"/",c);if(f==null)return null;let h=km(a);Ag(h);let g=null;for(let y=0;g==null&&y<h.length;++y){let m=Hg(f);g=Ug(h[y],m,s)}return g}function km(a,i=[],c=[],s=""){let o=(f,h,g)=>{let y={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:h,route:f};y.relativePath.startsWith("/")&&(Ce(y.relativePath.startsWith(s),`Absolute route path "${y.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),y.relativePath=y.relativePath.slice(s.length));let m=dl([s,y.relativePath]),v=c.concat(y);f.children&&f.children.length>0&&(Ce(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),km(f.children,i,v,m)),!(f.path==null&&!f.index)&&i.push({path:m,score:Mg(m,f.index),routesMeta:v})};return a.forEach((f,h)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))o(f,h);else for(let y of Ym(f.path))o(f,h,y)}),i}function Ym(a){let i=a.split("/");if(i.length===0)return[];let[c,...s]=i,o=c.endsWith("?"),f=c.replace(/\?$/,"");if(s.length===0)return o?[f,""]:[f];let h=Ym(s.join("/")),g=[];return g.push(...h.map(y=>y===""?f:[f,y].join("/"))),o&&g.push(...h),g.map(y=>a.startsWith("/")&&y===""?"/":y)}function Ag(a){a.sort((i,c)=>i.score!==c.score?c.score-i.score:Dg(i.routesMeta.map(s=>s.childrenIndex),c.routesMeta.map(s=>s.childrenIndex)))}var wg=/^:[\w-]+$/,Og=3,Ng=2,_g=1,zg=10,Cg=-2,om=a=>a==="*";function Mg(a,i){let c=a.split("/"),s=c.length;return c.some(om)&&(s+=Cg),i&&(s+=Ng),c.filter(o=>!om(o)).reduce((o,f)=>o+(wg.test(f)?Og:f===""?_g:zg),s)}function Dg(a,i){return a.length===i.length&&a.slice(0,-1).every((s,o)=>s===i[o])?a[a.length-1]-i[i.length-1]:0}function Ug(a,i,c=!1){let{routesMeta:s}=a,o={},f="/",h=[];for(let g=0;g<s.length;++g){let y=s[g],m=g===s.length-1,v=f==="/"?i:i.slice(f.length)||"/",A=Xi({path:y.relativePath,caseSensitive:y.caseSensitive,end:m},v),O=y.route;if(!A&&m&&c&&!s[s.length-1].route.index&&(A=Xi({path:y.relativePath,caseSensitive:y.caseSensitive,end:!1},v)),!A)return null;Object.assign(o,A.params),h.push({params:o,pathname:dl([f,A.pathname]),pathnameBase:kg(dl([f,A.pathnameBase])),route:O}),A.pathnameBase!=="/"&&(f=dl([f,A.pathnameBase]))}return h}function Xi(a,i){typeof a=="string"&&(a={path:a,caseSensitive:!1,end:!0});let[c,s]=jg(a.path,a.caseSensitive,a.end),o=i.match(c);if(!o)return null;let f=o[0],h=f.replace(/(.)\/+$/,"$1"),g=o.slice(1);return{params:s.reduce((m,{paramName:v,isOptional:A},O)=>{if(v==="*"){let R=g[O]||"";h=f.slice(0,f.length-R.length).replace(/(.)\/+$/,"$1")}const k=g[O];return A&&!k?m[v]=void 0:m[v]=(k||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:h,pattern:a}}function jg(a,i=!1,c=!0){Lt(a==="*"||!a.endsWith("*")||a.endsWith("/*"),`Route path "${a}" will be treated as if it were "${a.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${a.replace(/\*$/,"/*")}".`);let s=[],o="^"+a.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,g,y)=>(s.push({paramName:g,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return a.endsWith("*")?(s.push({paramName:"*"}),o+=a==="*"||a==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):c?o+="\\/*$":a!==""&&a!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,i?void 0:"i"),s]}function Hg(a){try{return a.split("/").map(i=>decodeURIComponent(i).replace(/\//g,"%2F")).join("/")}catch(i){return Lt(!1,`The URL path "${a}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${i}).`),a}}function hl(a,i){if(i==="/")return a;if(!a.toLowerCase().startsWith(i.toLowerCase()))return null;let c=i.endsWith("/")?i.length-1:i.length,s=a.charAt(c);return s&&s!=="/"?null:a.slice(c)||"/"}function Bg(a,i="/"){let{pathname:c,search:s="",hash:o=""}=typeof a=="string"?na(a):a;return{pathname:c?c.startsWith("/")?c:Lg(c,i):i,search:Yg(s),hash:Gg(o)}}function Lg(a,i){let c=i.replace(/\/+$/,"").split("/");return a.split("/").forEach(o=>{o===".."?c.length>1&&c.pop():o!=="."&&c.push(o)}),c.length>1?c.join("/"):"/"}function Tc(a,i,c,s){return`Cannot include a '${a}' character in a manually specified \`to.${i}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${c}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function qg(a){return a.filter((i,c)=>c===0||i.route.path&&i.route.path.length>0)}function Xc(a){let i=qg(a);return i.map((c,s)=>s===i.length-1?c.pathname:c.pathnameBase)}function Qc(a,i,c,s=!1){let o;typeof a=="string"?o=na(a):(o={...a},Ce(!o.pathname||!o.pathname.includes("?"),Tc("?","pathname","search",o)),Ce(!o.pathname||!o.pathname.includes("#"),Tc("#","pathname","hash",o)),Ce(!o.search||!o.search.includes("#"),Tc("#","search","hash",o)));let f=a===""||o.pathname==="",h=f?"/":o.pathname,g;if(h==null)g=c;else{let A=i.length-1;if(!s&&h.startsWith("..")){let O=h.split("/");for(;O[0]==="..";)O.shift(),A-=1;o.pathname=O.join("/")}g=A>=0?i[A]:"/"}let y=Bg(o,g),m=h&&h!=="/"&&h.endsWith("/"),v=(f||h===".")&&c.endsWith("/");return!y.pathname.endsWith("/")&&(m||v)&&(y.pathname+="/"),y}var dl=a=>a.join("/").replace(/\/\/+/g,"/"),kg=a=>a.replace(/\/+$/,"").replace(/^\/*/,"/"),Yg=a=>!a||a==="?"?"":a.startsWith("?")?a:"?"+a,Gg=a=>!a||a==="#"?"":a.startsWith("#")?a:"#"+a;function Vg(a){return a!=null&&typeof a.status=="number"&&typeof a.statusText=="string"&&typeof a.internal=="boolean"&&"data"in a}var Gm=["POST","PUT","PATCH","DELETE"];new Set(Gm);var Xg=["GET",...Gm];new Set(Xg);var aa=M.createContext(null);aa.displayName="DataRouter";var Ji=M.createContext(null);Ji.displayName="DataRouterState";var Vm=M.createContext({isTransitioning:!1});Vm.displayName="ViewTransition";var Qg=M.createContext(new Map);Qg.displayName="Fetchers";var Zg=M.createContext(null);Zg.displayName="Await";var qt=M.createContext(null);qt.displayName="Navigation";var yu=M.createContext(null);yu.displayName="Location";var $t=M.createContext({outlet:null,matches:[],isDataRoute:!1});$t.displayName="Route";var Zc=M.createContext(null);Zc.displayName="RouteError";function Kg(a,{relative:i}={}){Ce(ua(),"useHref() may be used only in the context of a <Router> component.");let{basename:c,navigator:s}=M.useContext(qt),{hash:o,pathname:f,search:h}=vu(a,{relative:i}),g=f;return c!=="/"&&(g=f==="/"?c:dl([c,f])),s.createHref({pathname:g,search:h,hash:o})}function ua(){return M.useContext(yu)!=null}function Vl(){return Ce(ua(),"useLocation() may be used only in the context of a <Router> component."),M.useContext(yu).location}var Xm="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Qm(a){M.useContext(qt).static||M.useLayoutEffect(a)}function gu(){let{isDataRoute:a}=M.useContext($t);return a?iv():Jg()}function Jg(){Ce(ua(),"useNavigate() may be used only in the context of a <Router> component.");let a=M.useContext(aa),{basename:i,navigator:c}=M.useContext(qt),{matches:s}=M.useContext($t),{pathname:o}=Vl(),f=JSON.stringify(Xc(s)),h=M.useRef(!1);return Qm(()=>{h.current=!0}),M.useCallback((y,m={})=>{if(Lt(h.current,Xm),!h.current)return;if(typeof y=="number"){c.go(y);return}let v=Qc(y,JSON.parse(f),o,m.relative==="path");a==null&&i!=="/"&&(v.pathname=v.pathname==="/"?i:dl([i,v.pathname])),(m.replace?c.replace:c.push)(v,m.state,m)},[i,c,f,o,a])}M.createContext(null);function vu(a,{relative:i}={}){let{matches:c}=M.useContext($t),{pathname:s}=Vl(),o=JSON.stringify(Xc(c));return M.useMemo(()=>Qc(a,JSON.parse(o),s,i==="path"),[a,o,s,i])}function $g(a,i){return Zm(a,i)}function Zm(a,i,c,s){var B;Ce(ua(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=M.useContext(qt),{matches:f}=M.useContext($t),h=f[f.length-1],g=h?h.params:{},y=h?h.pathname:"/",m=h?h.pathnameBase:"/",v=h&&h.route;{let G=v&&v.path||"";Km(y,!v||G.endsWith("*")||G.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${y}" (under <Route path="${G}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${G}"> to <Route path="${G==="/"?"*":`${G}/*`}">.`)}let A=Vl(),O;if(i){let G=typeof i=="string"?na(i):i;Ce(m==="/"||((B=G.pathname)==null?void 0:B.startsWith(m)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${G.pathname}" was given in the \`location\` prop.`),O=G}else O=A;let k=O.pathname||"/",R=k;if(m!=="/"){let G=m.replace(/^\//,"").split("/");R="/"+k.replace(/^\//,"").split("/").slice(G.length).join("/")}let z=qm(a,{pathname:R});Lt(v||z!=null,`No routes matched location "${O.pathname}${O.search}${O.hash}" `),Lt(z==null||z[z.length-1].route.element!==void 0||z[z.length-1].route.Component!==void 0||z[z.length-1].route.lazy!==void 0,`Matched leaf route at location "${O.pathname}${O.search}${O.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let U=ev(z&&z.map(G=>Object.assign({},G,{params:Object.assign({},g,G.params),pathname:dl([m,o.encodeLocation?o.encodeLocation(G.pathname).pathname:G.pathname]),pathnameBase:G.pathnameBase==="/"?m:dl([m,o.encodeLocation?o.encodeLocation(G.pathnameBase).pathname:G.pathnameBase])})),f,c,s);return i&&U?M.createElement(yu.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...O},navigationType:"POP"}},U):U}function Fg(){let a=uv(),i=Vg(a)?`${a.status} ${a.statusText}`:a instanceof Error?a.message:JSON.stringify(a),c=a instanceof Error?a.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},f={padding:"2px 4px",backgroundColor:s},h=null;return console.error("Error handled by React Router default ErrorBoundary:",a),h=M.createElement(M.Fragment,null,M.createElement("p",null,"💿 Hey developer 👋"),M.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",M.createElement("code",{style:f},"ErrorBoundary")," or"," ",M.createElement("code",{style:f},"errorElement")," prop on your route.")),M.createElement(M.Fragment,null,M.createElement("h2",null,"Unexpected Application Error!"),M.createElement("h3",{style:{fontStyle:"italic"}},i),c?M.createElement("pre",{style:o},c):null,h)}var Wg=M.createElement(Fg,null),Pg=class extends M.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,i){return i.location!==a.location||i.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:i.error,location:i.location,revalidation:a.revalidation||i.revalidation}}componentDidCatch(a,i){console.error("React Router caught the following error during render",a,i)}render(){return this.state.error!==void 0?M.createElement($t.Provider,{value:this.props.routeContext},M.createElement(Zc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Ig({routeContext:a,match:i,children:c}){let s=M.useContext(aa);return s&&s.static&&s.staticContext&&(i.route.errorElement||i.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=i.route.id),M.createElement($t.Provider,{value:a},c)}function ev(a,i=[],c=null,s=null){if(a==null){if(!c)return null;if(c.errors)a=c.matches;else if(i.length===0&&!c.initialized&&c.matches.length>0)a=c.matches;else return null}let o=a,f=c==null?void 0:c.errors;if(f!=null){let y=o.findIndex(m=>m.route.id&&(f==null?void 0:f[m.route.id])!==void 0);Ce(y>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,y+1))}let h=!1,g=-1;if(c)for(let y=0;y<o.length;y++){let m=o[y];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(g=y),m.route.id){let{loaderData:v,errors:A}=c,O=m.route.loader&&!v.hasOwnProperty(m.route.id)&&(!A||A[m.route.id]===void 0);if(m.route.lazy||O){h=!0,g>=0?o=o.slice(0,g+1):o=[o[0]];break}}}return o.reduceRight((y,m,v)=>{let A,O=!1,k=null,R=null;c&&(A=f&&m.route.id?f[m.route.id]:void 0,k=m.route.errorElement||Wg,h&&(g<0&&v===0?(Km("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),O=!0,R=null):g===v&&(O=!0,R=m.route.hydrateFallbackElement||null)));let z=i.concat(o.slice(0,v+1)),U=()=>{let B;return A?B=k:O?B=R:m.route.Component?B=M.createElement(m.route.Component,null):m.route.element?B=m.route.element:B=y,M.createElement(Ig,{match:m,routeContext:{outlet:y,matches:z,isDataRoute:c!=null},children:B})};return c&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?M.createElement(Pg,{location:c.location,revalidation:c.revalidation,component:k,error:A,children:U(),routeContext:{outlet:null,matches:z,isDataRoute:!0}}):U()},null)}function Kc(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function tv(a){let i=M.useContext(aa);return Ce(i,Kc(a)),i}function lv(a){let i=M.useContext(Ji);return Ce(i,Kc(a)),i}function nv(a){let i=M.useContext($t);return Ce(i,Kc(a)),i}function Jc(a){let i=nv(a),c=i.matches[i.matches.length-1];return Ce(c.route.id,`${a} can only be used on routes that contain a unique "id"`),c.route.id}function av(){return Jc("useRouteId")}function uv(){var s;let a=M.useContext(Zc),i=lv("useRouteError"),c=Jc("useRouteError");return a!==void 0?a:(s=i.errors)==null?void 0:s[c]}function iv(){let{router:a}=tv("useNavigate"),i=Jc("useNavigate"),c=M.useRef(!1);return Qm(()=>{c.current=!0}),M.useCallback(async(o,f={})=>{Lt(c.current,Xm),c.current&&(typeof o=="number"?a.navigate(o):await a.navigate(o,{fromRouteId:i,...f}))},[a,i])}var fm={};function Km(a,i,c){!i&&!fm[a]&&(fm[a]=!0,Lt(!1,c))}M.memo(rv);function rv({routes:a,future:i,state:c}){return Zm(a,void 0,c,i)}function Qi({to:a,replace:i,state:c,relative:s}){Ce(ua(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=M.useContext(qt);Lt(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=M.useContext($t),{pathname:h}=Vl(),g=gu(),y=Qc(a,Xc(f),h,s==="path"),m=JSON.stringify(y);return M.useEffect(()=>{g(JSON.parse(m),{replace:i,state:c,relative:s})},[g,m,s,i,c]),null}function ea(a){Ce(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function sv({basename:a="/",children:i=null,location:c,navigationType:s="POP",navigator:o,static:f=!1}){Ce(!ua(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=a.replace(/^\/*/,"/"),g=M.useMemo(()=>({basename:h,navigator:o,static:f,future:{}}),[h,o,f]);typeof c=="string"&&(c=na(c));let{pathname:y="/",search:m="",hash:v="",state:A=null,key:O="default"}=c,k=M.useMemo(()=>{let R=hl(y,h);return R==null?null:{location:{pathname:R,search:m,hash:v,state:A,key:O},navigationType:s}},[h,y,m,v,A,O,s]);return Lt(k!=null,`<Router basename="${h}"> is not able to match the URL "${y}${m}${v}" because it does not start with the basename, so the <Router> won't render anything.`),k==null?null:M.createElement(qt.Provider,{value:g},M.createElement(yu.Provider,{children:i,value:k}))}function cv({children:a,location:i}){return $g(Mc(a),i)}function Mc(a,i=[]){let c=[];return M.Children.forEach(a,(s,o)=>{if(!M.isValidElement(s))return;let f=[...i,o];if(s.type===M.Fragment){c.push.apply(c,Mc(s.props.children,f));return}Ce(s.type===ea,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Ce(!s.props.index||!s.props.children,"An index route cannot have child routes.");let h={id:s.props.id||f.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(h.children=Mc(s.props.children,f)),c.push(h)}),c}var qi="get",ki="application/x-www-form-urlencoded";function $i(a){return a!=null&&typeof a.tagName=="string"}function ov(a){return $i(a)&&a.tagName.toLowerCase()==="button"}function fv(a){return $i(a)&&a.tagName.toLowerCase()==="form"}function dv(a){return $i(a)&&a.tagName.toLowerCase()==="input"}function hv(a){return!!(a.metaKey||a.altKey||a.ctrlKey||a.shiftKey)}function mv(a,i){return a.button===0&&(!i||i==="_self")&&!hv(a)}var Hi=null;function pv(){if(Hi===null)try{new FormData(document.createElement("form"),0),Hi=!1}catch{Hi=!0}return Hi}var yv=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ac(a){return a!=null&&!yv.has(a)?(Lt(!1,`"${a}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ki}"`),null):a}function gv(a,i){let c,s,o,f,h;if(fv(a)){let g=a.getAttribute("action");s=g?hl(g,i):null,c=a.getAttribute("method")||qi,o=Ac(a.getAttribute("enctype"))||ki,f=new FormData(a)}else if(ov(a)||dv(a)&&(a.type==="submit"||a.type==="image")){let g=a.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let y=a.getAttribute("formaction")||g.getAttribute("action");if(s=y?hl(y,i):null,c=a.getAttribute("formmethod")||g.getAttribute("method")||qi,o=Ac(a.getAttribute("formenctype"))||Ac(g.getAttribute("enctype"))||ki,f=new FormData(g,a),!pv()){let{name:m,type:v,value:A}=a;if(v==="image"){let O=m?`${m}.`:"";f.append(`${O}x`,"0"),f.append(`${O}y`,"0")}else m&&f.append(m,A)}}else{if($i(a))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');c=qi,s=null,o=ki,h=a}return f&&o==="text/plain"&&(h=f,f=void 0),{action:s,method:c.toLowerCase(),encType:o,formData:f,body:h}}function $c(a,i){if(a===!1||a===null||typeof a>"u")throw new Error(i)}async function vv(a,i){if(a.id in i)return i[a.id];try{let c=await import(a.module);return i[a.id]=c,c}catch(c){return console.error(`Error loading route module \`${a.module}\`, reloading page...`),console.error(c),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function bv(a){return a==null?!1:a.href==null?a.rel==="preload"&&typeof a.imageSrcSet=="string"&&typeof a.imageSizes=="string":typeof a.rel=="string"&&typeof a.href=="string"}async function Sv(a,i,c){let s=await Promise.all(a.map(async o=>{let f=i.routes[o.route.id];if(f){let h=await vv(f,c);return h.links?h.links():[]}return[]}));return Tv(s.flat(1).filter(bv).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function dm(a,i,c,s,o,f){let h=(y,m)=>c[m]?y.route.id!==c[m].route.id:!0,g=(y,m)=>{var v;return c[m].pathname!==y.pathname||((v=c[m].route.path)==null?void 0:v.endsWith("*"))&&c[m].params["*"]!==y.params["*"]};return f==="assets"?i.filter((y,m)=>h(y,m)||g(y,m)):f==="data"?i.filter((y,m)=>{var A;let v=s.routes[y.route.id];if(!v||!v.hasLoader)return!1;if(h(y,m)||g(y,m))return!0;if(y.route.shouldRevalidate){let O=y.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:((A=c[0])==null?void 0:A.params)||{},nextUrl:new URL(a,window.origin),nextParams:y.params,defaultShouldRevalidate:!0});if(typeof O=="boolean")return O}return!0}):[]}function xv(a,i,{includeHydrateFallback:c}={}){return Ev(a.map(s=>{let o=i.routes[s.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),c&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function Ev(a){return[...new Set(a)]}function Rv(a){let i={},c=Object.keys(a).sort();for(let s of c)i[s]=a[s];return i}function Tv(a,i){let c=new Set;return new Set(i),a.reduce((s,o)=>{let f=JSON.stringify(Rv(o));return c.has(f)||(c.add(f),s.push({key:f,link:o})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Av=new Set([100,101,204,205]);function wv(a,i){let c=typeof a=="string"?new URL(a,typeof window>"u"?"server://singlefetch/":window.location.origin):a;return c.pathname==="/"?c.pathname="_root.data":i&&hl(c.pathname,i)==="/"?c.pathname=`${i.replace(/\/$/,"")}/_root.data`:c.pathname=`${c.pathname.replace(/\/$/,"")}.data`,c}function Jm(){let a=M.useContext(aa);return $c(a,"You must render this element inside a <DataRouterContext.Provider> element"),a}function Ov(){let a=M.useContext(Ji);return $c(a,"You must render this element inside a <DataRouterStateContext.Provider> element"),a}var Fc=M.createContext(void 0);Fc.displayName="FrameworkContext";function $m(){let a=M.useContext(Fc);return $c(a,"You must render this element inside a <HydratedRouter> element"),a}function Nv(a,i){let c=M.useContext(Fc),[s,o]=M.useState(!1),[f,h]=M.useState(!1),{onFocus:g,onBlur:y,onMouseEnter:m,onMouseLeave:v,onTouchStart:A}=i,O=M.useRef(null);M.useEffect(()=>{if(a==="render"&&h(!0),a==="viewport"){let z=B=>{B.forEach(G=>{h(G.isIntersecting)})},U=new IntersectionObserver(z,{threshold:.5});return O.current&&U.observe(O.current),()=>{U.disconnect()}}},[a]),M.useEffect(()=>{if(s){let z=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(z)}}},[s]);let k=()=>{o(!0)},R=()=>{o(!1),h(!1)};return c?a!=="intent"?[f,O,{}]:[f,O,{onFocus:ou(g,k),onBlur:ou(y,R),onMouseEnter:ou(m,k),onMouseLeave:ou(v,R),onTouchStart:ou(A,k)}]:[!1,O,{}]}function ou(a,i){return c=>{a&&a(c),c.defaultPrevented||i(c)}}function _v({page:a,...i}){let{router:c}=Jm(),s=M.useMemo(()=>qm(c.routes,a,c.basename),[c.routes,a,c.basename]);return s?M.createElement(Cv,{page:a,matches:s,...i}):null}function zv(a){let{manifest:i,routeModules:c}=$m(),[s,o]=M.useState([]);return M.useEffect(()=>{let f=!1;return Sv(a,i,c).then(h=>{f||o(h)}),()=>{f=!0}},[a,i,c]),s}function Cv({page:a,matches:i,...c}){let s=Vl(),{manifest:o,routeModules:f}=$m(),{basename:h}=Jm(),{loaderData:g,matches:y}=Ov(),m=M.useMemo(()=>dm(a,i,y,o,s,"data"),[a,i,y,o,s]),v=M.useMemo(()=>dm(a,i,y,o,s,"assets"),[a,i,y,o,s]),A=M.useMemo(()=>{if(a===s.pathname+s.search+s.hash)return[];let R=new Set,z=!1;if(i.forEach(B=>{var Q;let G=o.routes[B.route.id];!G||!G.hasLoader||(!m.some(le=>le.route.id===B.route.id)&&B.route.id in g&&((Q=f[B.route.id])!=null&&Q.shouldRevalidate)||G.hasClientLoader?z=!0:R.add(B.route.id))}),R.size===0)return[];let U=wv(a,h);return z&&R.size>0&&U.searchParams.set("_routes",i.filter(B=>R.has(B.route.id)).map(B=>B.route.id).join(",")),[U.pathname+U.search]},[h,g,s,o,m,i,a,f]),O=M.useMemo(()=>xv(v,o),[v,o]),k=zv(v);return M.createElement(M.Fragment,null,A.map(R=>M.createElement("link",{key:R,rel:"prefetch",as:"fetch",href:R,...c})),O.map(R=>M.createElement("link",{key:R,rel:"modulepreload",href:R,...c})),k.map(({key:R,link:z})=>M.createElement("link",{key:R,...z})))}function Mv(...a){return i=>{a.forEach(c=>{typeof c=="function"?c(i):c!=null&&(c.current=i)})}}var Fm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Fm&&(window.__reactRouterVersion="7.6.2")}catch{}function Dv({basename:a,children:i,window:c}){let s=M.useRef();s.current==null&&(s.current=Sg({window:c,v5Compat:!0}));let o=s.current,[f,h]=M.useState({action:o.action,location:o.location}),g=M.useCallback(y=>{M.startTransition(()=>h(y))},[h]);return M.useLayoutEffect(()=>o.listen(g),[o,g]),M.createElement(sv,{basename:a,children:i,location:f.location,navigationType:f.action,navigator:o})}var Wm=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pm=M.forwardRef(function({onClick:i,discover:c="render",prefetch:s="none",relative:o,reloadDocument:f,replace:h,state:g,target:y,to:m,preventScrollReset:v,viewTransition:A,...O},k){let{basename:R}=M.useContext(qt),z=typeof m=="string"&&Wm.test(m),U,B=!1;if(typeof m=="string"&&z&&(U=m,Fm))try{let he=new URL(window.location.href),Qe=m.startsWith("//")?new URL(he.protocol+m):new URL(m),ut=hl(Qe.pathname,R);Qe.origin===he.origin&&ut!=null?m=ut+Qe.search+Qe.hash:B=!0}catch{Lt(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let G=Kg(m,{relative:o}),[Q,le,K]=Nv(s,O),ce=Bv(m,{replace:h,state:g,target:y,preventScrollReset:v,relative:o,viewTransition:A});function J(he){i&&i(he),he.defaultPrevented||ce(he)}let $=M.createElement("a",{...O,...K,href:U||G,onClick:B||f?i:J,ref:Mv(k,le),target:y,"data-discover":!z&&c==="render"?"true":void 0});return Q&&!z?M.createElement(M.Fragment,null,$,M.createElement(_v,{page:G})):$});Pm.displayName="Link";var Uv=M.forwardRef(function({"aria-current":i="page",caseSensitive:c=!1,className:s="",end:o=!1,style:f,to:h,viewTransition:g,children:y,...m},v){let A=vu(h,{relative:m.relative}),O=Vl(),k=M.useContext(Ji),{navigator:R,basename:z}=M.useContext(qt),U=k!=null&&Gv(A)&&g===!0,B=R.encodeLocation?R.encodeLocation(A).pathname:A.pathname,G=O.pathname,Q=k&&k.navigation&&k.navigation.location?k.navigation.location.pathname:null;c||(G=G.toLowerCase(),Q=Q?Q.toLowerCase():null,B=B.toLowerCase()),Q&&z&&(Q=hl(Q,z)||Q);const le=B!=="/"&&B.endsWith("/")?B.length-1:B.length;let K=G===B||!o&&G.startsWith(B)&&G.charAt(le)==="/",ce=Q!=null&&(Q===B||!o&&Q.startsWith(B)&&Q.charAt(B.length)==="/"),J={isActive:K,isPending:ce,isTransitioning:U},$=K?i:void 0,he;typeof s=="function"?he=s(J):he=[s,K?"active":null,ce?"pending":null,U?"transitioning":null].filter(Boolean).join(" ");let Qe=typeof f=="function"?f(J):f;return M.createElement(Pm,{...m,"aria-current":$,className:he,ref:v,style:Qe,to:h,viewTransition:g},typeof y=="function"?y(J):y)});Uv.displayName="NavLink";var jv=M.forwardRef(({discover:a="render",fetcherKey:i,navigate:c,reloadDocument:s,replace:o,state:f,method:h=qi,action:g,onSubmit:y,relative:m,preventScrollReset:v,viewTransition:A,...O},k)=>{let R=kv(),z=Yv(g,{relative:m}),U=h.toLowerCase()==="get"?"get":"post",B=typeof g=="string"&&Wm.test(g),G=Q=>{if(y&&y(Q),Q.defaultPrevented)return;Q.preventDefault();let le=Q.nativeEvent.submitter,K=(le==null?void 0:le.getAttribute("formmethod"))||h;R(le||Q.currentTarget,{fetcherKey:i,method:K,navigate:c,replace:o,state:f,relative:m,preventScrollReset:v,viewTransition:A})};return M.createElement("form",{ref:k,method:U,action:z,onSubmit:s?y:G,...O,"data-discover":!B&&a==="render"?"true":void 0})});jv.displayName="Form";function Hv(a){return`${a} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Im(a){let i=M.useContext(aa);return Ce(i,Hv(a)),i}function Bv(a,{target:i,replace:c,state:s,preventScrollReset:o,relative:f,viewTransition:h}={}){let g=gu(),y=Vl(),m=vu(a,{relative:f});return M.useCallback(v=>{if(mv(v,i)){v.preventDefault();let A=c!==void 0?c:hu(y)===hu(m);g(a,{replace:A,state:s,preventScrollReset:o,relative:f,viewTransition:h})}},[y,g,m,c,s,i,a,o,f,h])}var Lv=0,qv=()=>`__${String(++Lv)}__`;function kv(){let{router:a}=Im("useSubmit"),{basename:i}=M.useContext(qt),c=av();return M.useCallback(async(s,o={})=>{let{action:f,method:h,encType:g,formData:y,body:m}=gv(s,i);if(o.navigate===!1){let v=o.fetcherKey||qv();await a.fetch(v,c,o.action||f,{preventScrollReset:o.preventScrollReset,formData:y,body:m,formMethod:o.method||h,formEncType:o.encType||g,flushSync:o.flushSync})}else await a.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:y,body:m,formMethod:o.method||h,formEncType:o.encType||g,replace:o.replace,state:o.state,fromRouteId:c,flushSync:o.flushSync,viewTransition:o.viewTransition})},[a,i,c])}function Yv(a,{relative:i}={}){let{basename:c}=M.useContext(qt),s=M.useContext($t);Ce(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),f={...vu(a||".",{relative:i})},h=Vl();if(a==null){f.search=h.search;let g=new URLSearchParams(f.search),y=g.getAll("index");if(y.some(v=>v==="")){g.delete("index"),y.filter(A=>A).forEach(A=>g.append("index",A));let v=g.toString();f.search=v?`?${v}`:""}}return(!a||a===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),c!=="/"&&(f.pathname=f.pathname==="/"?c:dl([c,f.pathname])),hu(f)}function Gv(a,i={}){let c=M.useContext(Vm);Ce(c!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=Im("useViewTransitionState"),o=vu(a,{relative:i.relative});if(!c.isTransitioning)return!1;let f=hl(c.currentLocation.pathname,s)||c.currentLocation.pathname,h=hl(c.nextLocation.pathname,s)||c.nextLocation.pathname;return Xi(o.pathname,h)!=null||Xi(o.pathname,f)!=null}[...Av];const hm=a=>{let i;const c=new Set,s=(m,v)=>{const A=typeof m=="function"?m(i):m;if(!Object.is(A,i)){const O=i;i=v??(typeof A!="object"||A===null)?A:Object.assign({},i,A),c.forEach(k=>k(i,O))}},o=()=>i,g={setState:s,getState:o,getInitialState:()=>y,subscribe:m=>(c.add(m),()=>c.delete(m))},y=i=a(s,o,g);return g},Vv=a=>a?hm(a):hm,Xv=a=>a;function Qv(a,i=Xv){const c=em.useSyncExternalStore(a.subscribe,()=>i(a.getState()),()=>i(a.getInitialState()));return em.useDebugValue(c),c}const mm=a=>{const i=Vv(a),c=s=>Qv(i,s);return Object.assign(c,i),c},ep=a=>a?mm(a):mm;function Zv(a,i){let c;try{c=a()}catch{return}return{getItem:o=>{var f;const h=y=>y===null?null:JSON.parse(y,void 0),g=(f=c.getItem(o))!=null?f:null;return g instanceof Promise?g.then(h):h(g)},setItem:(o,f)=>c.setItem(o,JSON.stringify(f,void 0)),removeItem:o=>c.removeItem(o)}}const Dc=a=>i=>{try{const c=a(i);return c instanceof Promise?c:{then(s){return Dc(s)(c)},catch(s){return this}}}catch(c){return{then(s){return this},catch(s){return Dc(s)(c)}}}},Kv=(a,i)=>(c,s,o)=>{let f={storage:Zv(()=>localStorage),partialize:z=>z,version:0,merge:(z,U)=>({...U,...z}),...i},h=!1;const g=new Set,y=new Set;let m=f.storage;if(!m)return a((...z)=>{console.warn(`[zustand persist middleware] Unable to update item '${f.name}', the given storage is currently unavailable.`),c(...z)},s,o);const v=()=>{const z=f.partialize({...s()});return m.setItem(f.name,{state:z,version:f.version})},A=o.setState;o.setState=(z,U)=>{A(z,U),v()};const O=a((...z)=>{c(...z),v()},s,o);o.getInitialState=()=>O;let k;const R=()=>{var z,U;if(!m)return;h=!1,g.forEach(G=>{var Q;return G((Q=s())!=null?Q:O)});const B=((U=f.onRehydrateStorage)==null?void 0:U.call(f,(z=s())!=null?z:O))||void 0;return Dc(m.getItem.bind(m))(f.name).then(G=>{if(G)if(typeof G.version=="number"&&G.version!==f.version){if(f.migrate){const Q=f.migrate(G.state,G.version);return Q instanceof Promise?Q.then(le=>[!0,le]):[!0,Q]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,G.state];return[!1,void 0]}).then(G=>{var Q;const[le,K]=G;if(k=f.merge(K,(Q=s())!=null?Q:O),c(k,!0),le)return v()}).then(()=>{B==null||B(k,void 0),k=s(),h=!0,y.forEach(G=>G(k))}).catch(G=>{B==null||B(void 0,G)})};return o.persist={setOptions:z=>{f={...f,...z},z.storage&&(m=z.storage)},clearStorage:()=>{m==null||m.removeItem(f.name)},getOptions:()=>f,rehydrate:()=>R(),hasHydrated:()=>h,onHydrate:z=>(g.add(z),()=>{g.delete(z)}),onFinishHydration:z=>(y.add(z),()=>{y.delete(z)})},f.skipHydration||R(),k||O},Jv=Kv,bu=ep()(Jv(a=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:i=>{a({user:i,isAuthenticated:!0})},setToken:i=>{a({token:i}),localStorage.setItem("smartpos_token",i)},login:(i,c)=>{a({user:i,token:c,isAuthenticated:!0,error:null}),localStorage.setItem("smartpos_token",c),localStorage.setItem("smartpos_user",JSON.stringify(i))},logout:()=>{a({user:null,token:null,isAuthenticated:!1,error:null}),localStorage.removeItem("smartpos_token"),localStorage.removeItem("smartpos_user")},setLoading:i=>{a({isLoading:i})},setError:i=>{a({error:i})},clearError:()=>{a({error:null})}}),{name:"smartpos-auth",partialize:a=>({user:a.user,token:a.token,isAuthenticated:a.isAuthenticated})}));function pm(a,i){if(typeof a=="function")return a(i);a!=null&&(a.current=i)}function $v(...a){return i=>{let c=!1;const s=a.map(o=>{const f=pm(o,i);return!c&&typeof f=="function"&&(c=!0),f});if(c)return()=>{for(let o=0;o<s.length;o++){const f=s[o];typeof f=="function"?f():pm(a[o],null)}}}}function Fv(a){const i=Pv(a),c=M.forwardRef((s,o)=>{const{children:f,...h}=s,g=M.Children.toArray(f),y=g.find(eb);if(y){const m=y.props.children,v=g.map(A=>A===y?M.Children.count(m)>1?M.Children.only(null):M.isValidElement(m)?m.props.children:null:A);return E.jsx(i,{...h,ref:o,children:M.isValidElement(m)?M.cloneElement(m,void 0,v):null})}return E.jsx(i,{...h,ref:o,children:f})});return c.displayName=`${a}.Slot`,c}var Wv=Fv("Slot");function Pv(a){const i=M.forwardRef((c,s)=>{const{children:o,...f}=c;if(M.isValidElement(o)){const h=lb(o),g=tb(f,o.props);return o.type!==M.Fragment&&(g.ref=s?$v(s,h):h),M.cloneElement(o,g)}return M.Children.count(o)>1?M.Children.only(null):null});return i.displayName=`${a}.SlotClone`,i}var Iv=Symbol("radix.slottable");function eb(a){return M.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===Iv}function tb(a,i){const c={...i};for(const s in i){const o=a[s],f=i[s];/^on[A-Z]/.test(s)?o&&f?c[s]=(...g)=>{const y=f(...g);return o(...g),y}:o&&(c[s]=o):s==="style"?c[s]={...o,...f}:s==="className"&&(c[s]=[o,f].filter(Boolean).join(" "))}return{...a,...c}}function lb(a){var s,o;let i=(s=Object.getOwnPropertyDescriptor(a.props,"ref"))==null?void 0:s.get,c=i&&"isReactWarning"in i&&i.isReactWarning;return c?a.ref:(i=(o=Object.getOwnPropertyDescriptor(a,"ref"))==null?void 0:o.get,c=i&&"isReactWarning"in i&&i.isReactWarning,c?a.props.ref:a.props.ref||a.ref)}function tp(a){var i,c,s="";if(typeof a=="string"||typeof a=="number")s+=a;else if(typeof a=="object")if(Array.isArray(a)){var o=a.length;for(i=0;i<o;i++)a[i]&&(c=tp(a[i]))&&(s&&(s+=" "),s+=c)}else for(c in a)a[c]&&(s&&(s+=" "),s+=c);return s}function lp(){for(var a,i,c=0,s="",o=arguments.length;c<o;c++)(a=arguments[c])&&(i=tp(a))&&(s&&(s+=" "),s+=i);return s}const ym=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,gm=lp,nb=(a,i)=>c=>{var s;if((i==null?void 0:i.variants)==null)return gm(a,c==null?void 0:c.class,c==null?void 0:c.className);const{variants:o,defaultVariants:f}=i,h=Object.keys(o).map(m=>{const v=c==null?void 0:c[m],A=f==null?void 0:f[m];if(v===null)return null;const O=ym(v)||ym(A);return o[m][O]}),g=c&&Object.entries(c).reduce((m,v)=>{let[A,O]=v;return O===void 0||(m[A]=O),m},{}),y=i==null||(s=i.compoundVariants)===null||s===void 0?void 0:s.reduce((m,v)=>{let{class:A,className:O,...k}=v;return Object.entries(k).every(R=>{let[z,U]=R;return Array.isArray(U)?U.includes({...f,...g}[z]):{...f,...g}[z]===U})?[...m,A,O]:m},[]);return gm(a,h,y,c==null?void 0:c.class,c==null?void 0:c.className)},Wc="-",ab=a=>{const i=ib(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:s}=a;return{getClassGroupId:h=>{const g=h.split(Wc);return g[0]===""&&g.length!==1&&g.shift(),np(g,i)||ub(h)},getConflictingClassGroupIds:(h,g)=>{const y=c[h]||[];return g&&s[h]?[...y,...s[h]]:y}}},np=(a,i)=>{var h;if(a.length===0)return i.classGroupId;const c=a[0],s=i.nextPart.get(c),o=s?np(a.slice(1),s):void 0;if(o)return o;if(i.validators.length===0)return;const f=a.join(Wc);return(h=i.validators.find(({validator:g})=>g(f)))==null?void 0:h.classGroupId},vm=/^\[(.+)\]$/,ub=a=>{if(vm.test(a)){const i=vm.exec(a)[1],c=i==null?void 0:i.substring(0,i.indexOf(":"));if(c)return"arbitrary.."+c}},ib=a=>{const{theme:i,classGroups:c}=a,s={nextPart:new Map,validators:[]};for(const o in c)Uc(c[o],s,o,i);return s},Uc=(a,i,c,s)=>{a.forEach(o=>{if(typeof o=="string"){const f=o===""?i:bm(i,o);f.classGroupId=c;return}if(typeof o=="function"){if(rb(o)){Uc(o(s),i,c,s);return}i.validators.push({validator:o,classGroupId:c});return}Object.entries(o).forEach(([f,h])=>{Uc(h,bm(i,f),c,s)})})},bm=(a,i)=>{let c=a;return i.split(Wc).forEach(s=>{c.nextPart.has(s)||c.nextPart.set(s,{nextPart:new Map,validators:[]}),c=c.nextPart.get(s)}),c},rb=a=>a.isThemeGetter,sb=a=>{if(a<1)return{get:()=>{},set:()=>{}};let i=0,c=new Map,s=new Map;const o=(f,h)=>{c.set(f,h),i++,i>a&&(i=0,s=c,c=new Map)};return{get(f){let h=c.get(f);if(h!==void 0)return h;if((h=s.get(f))!==void 0)return o(f,h),h},set(f,h){c.has(f)?c.set(f,h):o(f,h)}}},jc="!",Hc=":",cb=Hc.length,ob=a=>{const{prefix:i,experimentalParseClassName:c}=a;let s=o=>{const f=[];let h=0,g=0,y=0,m;for(let R=0;R<o.length;R++){let z=o[R];if(h===0&&g===0){if(z===Hc){f.push(o.slice(y,R)),y=R+cb;continue}if(z==="/"){m=R;continue}}z==="["?h++:z==="]"?h--:z==="("?g++:z===")"&&g--}const v=f.length===0?o:o.substring(y),A=fb(v),O=A!==v,k=m&&m>y?m-y:void 0;return{modifiers:f,hasImportantModifier:O,baseClassName:A,maybePostfixModifierPosition:k}};if(i){const o=i+Hc,f=s;s=h=>h.startsWith(o)?f(h.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:h,maybePostfixModifierPosition:void 0}}if(c){const o=s;s=f=>c({className:f,parseClassName:o})}return s},fb=a=>a.endsWith(jc)?a.substring(0,a.length-1):a.startsWith(jc)?a.substring(1):a,db=a=>{const i=Object.fromEntries(a.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const o=[];let f=[];return s.forEach(h=>{h[0]==="["||i[h]?(o.push(...f.sort(),h),f=[]):f.push(h)}),o.push(...f.sort()),o}},hb=a=>({cache:sb(a.cacheSize),parseClassName:ob(a),sortModifiers:db(a),...ab(a)}),mb=/\s+/,pb=(a,i)=>{const{parseClassName:c,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:f}=i,h=[],g=a.trim().split(mb);let y="";for(let m=g.length-1;m>=0;m-=1){const v=g[m],{isExternal:A,modifiers:O,hasImportantModifier:k,baseClassName:R,maybePostfixModifierPosition:z}=c(v);if(A){y=v+(y.length>0?" "+y:y);continue}let U=!!z,B=s(U?R.substring(0,z):R);if(!B){if(!U){y=v+(y.length>0?" "+y:y);continue}if(B=s(R),!B){y=v+(y.length>0?" "+y:y);continue}U=!1}const G=f(O).join(":"),Q=k?G+jc:G,le=Q+B;if(h.includes(le))continue;h.push(le);const K=o(B,U);for(let ce=0;ce<K.length;++ce){const J=K[ce];h.push(Q+J)}y=v+(y.length>0?" "+y:y)}return y};function yb(){let a=0,i,c,s="";for(;a<arguments.length;)(i=arguments[a++])&&(c=ap(i))&&(s&&(s+=" "),s+=c);return s}const ap=a=>{if(typeof a=="string")return a;let i,c="";for(let s=0;s<a.length;s++)a[s]&&(i=ap(a[s]))&&(c&&(c+=" "),c+=i);return c};function gb(a,...i){let c,s,o,f=h;function h(y){const m=i.reduce((v,A)=>A(v),a());return c=hb(m),s=c.cache.get,o=c.cache.set,f=g,g(y)}function g(y){const m=s(y);if(m)return m;const v=pb(y,c);return o(y,v),v}return function(){return f(yb.apply(null,arguments))}}const Xe=a=>{const i=c=>c[a]||[];return i.isThemeGetter=!0,i},up=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,ip=/^\((?:(\w[\w-]*):)?(.+)\)$/i,vb=/^\d+\/\d+$/,bb=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Sb=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,xb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Eb=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Rb=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,In=a=>vb.test(a),fe=a=>!!a&&!Number.isNaN(Number(a)),Yl=a=>!!a&&Number.isInteger(Number(a)),wc=a=>a.endsWith("%")&&fe(a.slice(0,-1)),fl=a=>bb.test(a),Tb=()=>!0,Ab=a=>Sb.test(a)&&!xb.test(a),rp=()=>!1,wb=a=>Eb.test(a),Ob=a=>Rb.test(a),Nb=a=>!I(a)&&!ee(a),_b=a=>ia(a,op,rp),I=a=>up.test(a),on=a=>ia(a,fp,Ab),Oc=a=>ia(a,Ub,fe),Sm=a=>ia(a,sp,rp),zb=a=>ia(a,cp,Ob),Bi=a=>ia(a,dp,wb),ee=a=>ip.test(a),fu=a=>ra(a,fp),Cb=a=>ra(a,jb),xm=a=>ra(a,sp),Mb=a=>ra(a,op),Db=a=>ra(a,cp),Li=a=>ra(a,dp,!0),ia=(a,i,c)=>{const s=up.exec(a);return s?s[1]?i(s[1]):c(s[2]):!1},ra=(a,i,c=!1)=>{const s=ip.exec(a);return s?s[1]?i(s[1]):c:!1},sp=a=>a==="position"||a==="percentage",cp=a=>a==="image"||a==="url",op=a=>a==="length"||a==="size"||a==="bg-size",fp=a=>a==="length",Ub=a=>a==="number",jb=a=>a==="family-name",dp=a=>a==="shadow",Hb=()=>{const a=Xe("color"),i=Xe("font"),c=Xe("text"),s=Xe("font-weight"),o=Xe("tracking"),f=Xe("leading"),h=Xe("breakpoint"),g=Xe("container"),y=Xe("spacing"),m=Xe("radius"),v=Xe("shadow"),A=Xe("inset-shadow"),O=Xe("text-shadow"),k=Xe("drop-shadow"),R=Xe("blur"),z=Xe("perspective"),U=Xe("aspect"),B=Xe("ease"),G=Xe("animate"),Q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],le=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],K=()=>[...le(),ee,I],ce=()=>["auto","hidden","clip","visible","scroll"],J=()=>["auto","contain","none"],$=()=>[ee,I,y],he=()=>[In,"full","auto",...$()],Qe=()=>[Yl,"none","subgrid",ee,I],ut=()=>["auto",{span:["full",Yl,ee,I]},Yl,ee,I],He=()=>[Yl,"auto",ee,I],Yt=()=>["auto","min","max","fr",ee,I],Ut=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Me=()=>["start","end","center","stretch","center-safe","end-safe"],j=()=>["auto",...$()],Z=()=>[In,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],V=()=>[a,ee,I],xe=()=>[...le(),xm,Sm,{position:[ee,I]}],S=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Y=()=>["auto","cover","contain",Mb,_b,{size:[ee,I]}],F=()=>[wc,fu,on],X=()=>["","none","full",m,ee,I],W=()=>["",fe,fu,on],me=()=>["solid","dashed","dotted","double"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ve=()=>[fe,wc,xm,Sm],Oe=()=>["","none",R,ee,I],pt=()=>["none",fe,ee,I],ml=()=>["none",fe,ee,I],pl=()=>[fe,ee,I],yl=()=>[In,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[fl],breakpoint:[fl],color:[Tb],container:[fl],"drop-shadow":[fl],ease:["in","out","in-out"],font:[Nb],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[fl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[fl],shadow:[fl],spacing:["px",fe],text:[fl],"text-shadow":[fl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",In,I,ee,U]}],container:["container"],columns:[{columns:[fe,I,ee,g]}],"break-after":[{"break-after":Q()}],"break-before":[{"break-before":Q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:K()}],overflow:[{overflow:ce()}],"overflow-x":[{"overflow-x":ce()}],"overflow-y":[{"overflow-y":ce()}],overscroll:[{overscroll:J()}],"overscroll-x":[{"overscroll-x":J()}],"overscroll-y":[{"overscroll-y":J()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:he()}],"inset-x":[{"inset-x":he()}],"inset-y":[{"inset-y":he()}],start:[{start:he()}],end:[{end:he()}],top:[{top:he()}],right:[{right:he()}],bottom:[{bottom:he()}],left:[{left:he()}],visibility:["visible","invisible","collapse"],z:[{z:[Yl,"auto",ee,I]}],basis:[{basis:[In,"full","auto",g,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[fe,In,"auto","initial","none",I]}],grow:[{grow:["",fe,ee,I]}],shrink:[{shrink:["",fe,ee,I]}],order:[{order:[Yl,"first","last","none",ee,I]}],"grid-cols":[{"grid-cols":Qe()}],"col-start-end":[{col:ut()}],"col-start":[{"col-start":He()}],"col-end":[{"col-end":He()}],"grid-rows":[{"grid-rows":Qe()}],"row-start-end":[{row:ut()}],"row-start":[{"row-start":He()}],"row-end":[{"row-end":He()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Yt()}],"auto-rows":[{"auto-rows":Yt()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...Ut(),"normal"]}],"justify-items":[{"justify-items":[...Me(),"normal"]}],"justify-self":[{"justify-self":["auto",...Me()]}],"align-content":[{content:["normal",...Ut()]}],"align-items":[{items:[...Me(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Me(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ut()}],"place-items":[{"place-items":[...Me(),"baseline"]}],"place-self":[{"place-self":["auto",...Me()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:j()}],mx:[{mx:j()}],my:[{my:j()}],ms:[{ms:j()}],me:[{me:j()}],mt:[{mt:j()}],mr:[{mr:j()}],mb:[{mb:j()}],ml:[{ml:j()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[g,"screen",...Z()]}],"min-w":[{"min-w":[g,"screen","none",...Z()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[h]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",c,fu,on]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,ee,Oc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",wc,I]}],"font-family":[{font:[Cb,I,i]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,ee,I]}],"line-clamp":[{"line-clamp":[fe,"none",ee,Oc]}],leading:[{leading:[f,...$()]}],"list-image":[{"list-image":["none",ee,I]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ee,I]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...me(),"wavy"]}],"text-decoration-thickness":[{decoration:[fe,"from-font","auto",ee,on]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[fe,"auto",ee,I]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ee,I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ee,I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:xe()}],"bg-repeat":[{bg:S()}],"bg-size":[{bg:Y()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Yl,ee,I],radial:["",ee,I],conic:[Yl,ee,I]},Db,zb]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:X()}],"rounded-s":[{"rounded-s":X()}],"rounded-e":[{"rounded-e":X()}],"rounded-t":[{"rounded-t":X()}],"rounded-r":[{"rounded-r":X()}],"rounded-b":[{"rounded-b":X()}],"rounded-l":[{"rounded-l":X()}],"rounded-ss":[{"rounded-ss":X()}],"rounded-se":[{"rounded-se":X()}],"rounded-ee":[{"rounded-ee":X()}],"rounded-es":[{"rounded-es":X()}],"rounded-tl":[{"rounded-tl":X()}],"rounded-tr":[{"rounded-tr":X()}],"rounded-br":[{"rounded-br":X()}],"rounded-bl":[{"rounded-bl":X()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...me(),"hidden","none"]}],"divide-style":[{divide:[...me(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...me(),"none","hidden"]}],"outline-offset":[{"outline-offset":[fe,ee,I]}],"outline-w":[{outline:["",fe,fu,on]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",v,Li,Bi]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",A,Li,Bi]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[fe,on]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",O,Li,Bi]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[fe,ee,I]}],"mix-blend":[{"mix-blend":[...ie(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ie()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[fe]}],"mask-image-linear-from-pos":[{"mask-linear-from":ve()}],"mask-image-linear-to-pos":[{"mask-linear-to":ve()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":ve()}],"mask-image-t-to-pos":[{"mask-t-to":ve()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":ve()}],"mask-image-r-to-pos":[{"mask-r-to":ve()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":ve()}],"mask-image-b-to-pos":[{"mask-b-to":ve()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":ve()}],"mask-image-l-to-pos":[{"mask-l-to":ve()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":ve()}],"mask-image-x-to-pos":[{"mask-x-to":ve()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":ve()}],"mask-image-y-to-pos":[{"mask-y-to":ve()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[ee,I]}],"mask-image-radial-from-pos":[{"mask-radial-from":ve()}],"mask-image-radial-to-pos":[{"mask-radial-to":ve()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":le()}],"mask-image-conic-pos":[{"mask-conic":[fe]}],"mask-image-conic-from-pos":[{"mask-conic-from":ve()}],"mask-image-conic-to-pos":[{"mask-conic-to":ve()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:xe()}],"mask-repeat":[{mask:S()}],"mask-size":[{mask:Y()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ee,I]}],filter:[{filter:["","none",ee,I]}],blur:[{blur:Oe()}],brightness:[{brightness:[fe,ee,I]}],contrast:[{contrast:[fe,ee,I]}],"drop-shadow":[{"drop-shadow":["","none",k,Li,Bi]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",fe,ee,I]}],"hue-rotate":[{"hue-rotate":[fe,ee,I]}],invert:[{invert:["",fe,ee,I]}],saturate:[{saturate:[fe,ee,I]}],sepia:[{sepia:["",fe,ee,I]}],"backdrop-filter":[{"backdrop-filter":["","none",ee,I]}],"backdrop-blur":[{"backdrop-blur":Oe()}],"backdrop-brightness":[{"backdrop-brightness":[fe,ee,I]}],"backdrop-contrast":[{"backdrop-contrast":[fe,ee,I]}],"backdrop-grayscale":[{"backdrop-grayscale":["",fe,ee,I]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[fe,ee,I]}],"backdrop-invert":[{"backdrop-invert":["",fe,ee,I]}],"backdrop-opacity":[{"backdrop-opacity":[fe,ee,I]}],"backdrop-saturate":[{"backdrop-saturate":[fe,ee,I]}],"backdrop-sepia":[{"backdrop-sepia":["",fe,ee,I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ee,I]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[fe,"initial",ee,I]}],ease:[{ease:["linear","initial",B,ee,I]}],delay:[{delay:[fe,ee,I]}],animate:[{animate:["none",G,ee,I]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[z,ee,I]}],"perspective-origin":[{"perspective-origin":K()}],rotate:[{rotate:pt()}],"rotate-x":[{"rotate-x":pt()}],"rotate-y":[{"rotate-y":pt()}],"rotate-z":[{"rotate-z":pt()}],scale:[{scale:ml()}],"scale-x":[{"scale-x":ml()}],"scale-y":[{"scale-y":ml()}],"scale-z":[{"scale-z":ml()}],"scale-3d":["scale-3d"],skew:[{skew:pl()}],"skew-x":[{"skew-x":pl()}],"skew-y":[{"skew-y":pl()}],transform:[{transform:[ee,I,"","none","gpu","cpu"]}],"transform-origin":[{origin:K()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:yl()}],"translate-x":[{"translate-x":yl()}],"translate-y":[{"translate-y":yl()}],"translate-z":[{"translate-z":yl()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ee,I]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ee,I]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[fe,fu,on,Oc]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Bb=gb(Hb);function Xl(...a){return Bb(lp(a))}const Lb=nb("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),rt=M.forwardRef(({className:a,variant:i,size:c,asChild:s=!1,...o},f)=>{const h=s?Wv:"button";return E.jsx(h,{className:Xl(Lb({variant:i,size:c,className:a})),ref:f,...o})});rt.displayName="Button";const mu=M.forwardRef(({className:a,type:i,...c},s)=>E.jsx("input",{type:i,className:Xl("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...c}));mu.displayName="Input";const ta=M.forwardRef(({className:a,...i},c)=>E.jsx("div",{ref:c,className:Xl("rounded-lg border bg-card text-card-foreground shadow-sm",a),...i}));ta.displayName="Card";const Fi=M.forwardRef(({className:a,...i},c)=>E.jsx("div",{ref:c,className:Xl("flex flex-col space-y-1.5 p-6",a),...i}));Fi.displayName="CardHeader";const Wi=M.forwardRef(({className:a,...i},c)=>E.jsx("h3",{ref:c,className:Xl("text-2xl font-semibold leading-none tracking-tight",a),...i}));Wi.displayName="CardTitle";const Pc=M.forwardRef(({className:a,...i},c)=>E.jsx("p",{ref:c,className:Xl("text-sm text-muted-foreground",a),...i}));Pc.displayName="CardDescription";const la=M.forwardRef(({className:a,...i},c)=>E.jsx("div",{ref:c,className:Xl("p-6 pt-0",a),...i}));la.displayName="CardContent";const qb=M.forwardRef(({className:a,...i},c)=>E.jsx("div",{ref:c,className:Xl("flex items-center p-6 pt-0",a),...i}));qb.displayName="CardFooter";function hp(a,i){return function(){return a.apply(i,arguments)}}const{toString:kb}=Object.prototype,{getPrototypeOf:Ic}=Object,{iterator:Pi,toStringTag:mp}=Symbol,Ii=(a=>i=>{const c=kb.call(i);return a[c]||(a[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),kt=a=>(a=a.toLowerCase(),i=>Ii(i)===a),er=a=>i=>typeof i===a,{isArray:sa}=Array,pu=er("undefined");function Yb(a){return a!==null&&!pu(a)&&a.constructor!==null&&!pu(a.constructor)&&ht(a.constructor.isBuffer)&&a.constructor.isBuffer(a)}const pp=kt("ArrayBuffer");function Gb(a){let i;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?i=ArrayBuffer.isView(a):i=a&&a.buffer&&pp(a.buffer),i}const Vb=er("string"),ht=er("function"),yp=er("number"),tr=a=>a!==null&&typeof a=="object",Xb=a=>a===!0||a===!1,Yi=a=>{if(Ii(a)!=="object")return!1;const i=Ic(a);return(i===null||i===Object.prototype||Object.getPrototypeOf(i)===null)&&!(mp in a)&&!(Pi in a)},Qb=kt("Date"),Zb=kt("File"),Kb=kt("Blob"),Jb=kt("FileList"),$b=a=>tr(a)&&ht(a.pipe),Fb=a=>{let i;return a&&(typeof FormData=="function"&&a instanceof FormData||ht(a.append)&&((i=Ii(a))==="formdata"||i==="object"&&ht(a.toString)&&a.toString()==="[object FormData]"))},Wb=kt("URLSearchParams"),[Pb,Ib,e1,t1]=["ReadableStream","Request","Response","Headers"].map(kt),l1=a=>a.trim?a.trim():a.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Su(a,i,{allOwnKeys:c=!1}={}){if(a===null||typeof a>"u")return;let s,o;if(typeof a!="object"&&(a=[a]),sa(a))for(s=0,o=a.length;s<o;s++)i.call(null,a[s],s,a);else{const f=c?Object.getOwnPropertyNames(a):Object.keys(a),h=f.length;let g;for(s=0;s<h;s++)g=f[s],i.call(null,a[g],g,a)}}function gp(a,i){i=i.toLowerCase();const c=Object.keys(a);let s=c.length,o;for(;s-- >0;)if(o=c[s],i===o.toLowerCase())return o;return null}const fn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,vp=a=>!pu(a)&&a!==fn;function Bc(){const{caseless:a}=vp(this)&&this||{},i={},c=(s,o)=>{const f=a&&gp(i,o)||o;Yi(i[f])&&Yi(s)?i[f]=Bc(i[f],s):Yi(s)?i[f]=Bc({},s):sa(s)?i[f]=s.slice():i[f]=s};for(let s=0,o=arguments.length;s<o;s++)arguments[s]&&Su(arguments[s],c);return i}const n1=(a,i,c,{allOwnKeys:s}={})=>(Su(i,(o,f)=>{c&&ht(o)?a[f]=hp(o,c):a[f]=o},{allOwnKeys:s}),a),a1=a=>(a.charCodeAt(0)===65279&&(a=a.slice(1)),a),u1=(a,i,c,s)=>{a.prototype=Object.create(i.prototype,s),a.prototype.constructor=a,Object.defineProperty(a,"super",{value:i.prototype}),c&&Object.assign(a.prototype,c)},i1=(a,i,c,s)=>{let o,f,h;const g={};if(i=i||{},a==null)return i;do{for(o=Object.getOwnPropertyNames(a),f=o.length;f-- >0;)h=o[f],(!s||s(h,a,i))&&!g[h]&&(i[h]=a[h],g[h]=!0);a=c!==!1&&Ic(a)}while(a&&(!c||c(a,i))&&a!==Object.prototype);return i},r1=(a,i,c)=>{a=String(a),(c===void 0||c>a.length)&&(c=a.length),c-=i.length;const s=a.indexOf(i,c);return s!==-1&&s===c},s1=a=>{if(!a)return null;if(sa(a))return a;let i=a.length;if(!yp(i))return null;const c=new Array(i);for(;i-- >0;)c[i]=a[i];return c},c1=(a=>i=>a&&i instanceof a)(typeof Uint8Array<"u"&&Ic(Uint8Array)),o1=(a,i)=>{const s=(a&&a[Pi]).call(a);let o;for(;(o=s.next())&&!o.done;){const f=o.value;i.call(a,f[0],f[1])}},f1=(a,i)=>{let c;const s=[];for(;(c=a.exec(i))!==null;)s.push(c);return s},d1=kt("HTMLFormElement"),h1=a=>a.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,s,o){return s.toUpperCase()+o}),Em=(({hasOwnProperty:a})=>(i,c)=>a.call(i,c))(Object.prototype),m1=kt("RegExp"),bp=(a,i)=>{const c=Object.getOwnPropertyDescriptors(a),s={};Su(c,(o,f)=>{let h;(h=i(o,f,a))!==!1&&(s[f]=h||o)}),Object.defineProperties(a,s)},p1=a=>{bp(a,(i,c)=>{if(ht(a)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const s=a[c];if(ht(s)){if(i.enumerable=!1,"writable"in i){i.writable=!1;return}i.set||(i.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},y1=(a,i)=>{const c={},s=o=>{o.forEach(f=>{c[f]=!0})};return sa(a)?s(a):s(String(a).split(i)),c},g1=()=>{},v1=(a,i)=>a!=null&&Number.isFinite(a=+a)?a:i;function b1(a){return!!(a&&ht(a.append)&&a[mp]==="FormData"&&a[Pi])}const S1=a=>{const i=new Array(10),c=(s,o)=>{if(tr(s)){if(i.indexOf(s)>=0)return;if(!("toJSON"in s)){i[o]=s;const f=sa(s)?[]:{};return Su(s,(h,g)=>{const y=c(h,o+1);!pu(y)&&(f[g]=y)}),i[o]=void 0,f}}return s};return c(a,0)},x1=kt("AsyncFunction"),E1=a=>a&&(tr(a)||ht(a))&&ht(a.then)&&ht(a.catch),Sp=((a,i)=>a?setImmediate:i?((c,s)=>(fn.addEventListener("message",({source:o,data:f})=>{o===fn&&f===c&&s.length&&s.shift()()},!1),o=>{s.push(o),fn.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",ht(fn.postMessage)),R1=typeof queueMicrotask<"u"?queueMicrotask.bind(fn):typeof process<"u"&&process.nextTick||Sp,T1=a=>a!=null&&ht(a[Pi]),D={isArray:sa,isArrayBuffer:pp,isBuffer:Yb,isFormData:Fb,isArrayBufferView:Gb,isString:Vb,isNumber:yp,isBoolean:Xb,isObject:tr,isPlainObject:Yi,isReadableStream:Pb,isRequest:Ib,isResponse:e1,isHeaders:t1,isUndefined:pu,isDate:Qb,isFile:Zb,isBlob:Kb,isRegExp:m1,isFunction:ht,isStream:$b,isURLSearchParams:Wb,isTypedArray:c1,isFileList:Jb,forEach:Su,merge:Bc,extend:n1,trim:l1,stripBOM:a1,inherits:u1,toFlatObject:i1,kindOf:Ii,kindOfTest:kt,endsWith:r1,toArray:s1,forEachEntry:o1,matchAll:f1,isHTMLForm:d1,hasOwnProperty:Em,hasOwnProp:Em,reduceDescriptors:bp,freezeMethods:p1,toObjectSet:y1,toCamelCase:h1,noop:g1,toFiniteNumber:v1,findKey:gp,global:fn,isContextDefined:vp,isSpecCompliantForm:b1,toJSONObject:S1,isAsyncFn:x1,isThenable:E1,setImmediate:Sp,asap:R1,isIterable:T1};function re(a,i,c,s,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=a,this.name="AxiosError",i&&(this.code=i),c&&(this.config=c),s&&(this.request=s),o&&(this.response=o,this.status=o.status?o.status:null)}D.inherits(re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const xp=re.prototype,Ep={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(a=>{Ep[a]={value:a}});Object.defineProperties(re,Ep);Object.defineProperty(xp,"isAxiosError",{value:!0});re.from=(a,i,c,s,o,f)=>{const h=Object.create(xp);return D.toFlatObject(a,h,function(y){return y!==Error.prototype},g=>g!=="isAxiosError"),re.call(h,a.message,i,c,s,o),h.cause=a,h.name=a.name,f&&Object.assign(h,f),h};const A1=null;function Lc(a){return D.isPlainObject(a)||D.isArray(a)}function Rp(a){return D.endsWith(a,"[]")?a.slice(0,-2):a}function Rm(a,i,c){return a?a.concat(i).map(function(o,f){return o=Rp(o),!c&&f?"["+o+"]":o}).join(c?".":""):i}function w1(a){return D.isArray(a)&&!a.some(Lc)}const O1=D.toFlatObject(D,{},null,function(i){return/^is[A-Z]/.test(i)});function lr(a,i,c){if(!D.isObject(a))throw new TypeError("target must be an object");i=i||new FormData,c=D.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(z,U){return!D.isUndefined(U[z])});const s=c.metaTokens,o=c.visitor||v,f=c.dots,h=c.indexes,y=(c.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(i);if(!D.isFunction(o))throw new TypeError("visitor must be a function");function m(R){if(R===null)return"";if(D.isDate(R))return R.toISOString();if(!y&&D.isBlob(R))throw new re("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(R)||D.isTypedArray(R)?y&&typeof Blob=="function"?new Blob([R]):Buffer.from(R):R}function v(R,z,U){let B=R;if(R&&!U&&typeof R=="object"){if(D.endsWith(z,"{}"))z=s?z:z.slice(0,-2),R=JSON.stringify(R);else if(D.isArray(R)&&w1(R)||(D.isFileList(R)||D.endsWith(z,"[]"))&&(B=D.toArray(R)))return z=Rp(z),B.forEach(function(Q,le){!(D.isUndefined(Q)||Q===null)&&i.append(h===!0?Rm([z],le,f):h===null?z:z+"[]",m(Q))}),!1}return Lc(R)?!0:(i.append(Rm(U,z,f),m(R)),!1)}const A=[],O=Object.assign(O1,{defaultVisitor:v,convertValue:m,isVisitable:Lc});function k(R,z){if(!D.isUndefined(R)){if(A.indexOf(R)!==-1)throw Error("Circular reference detected in "+z.join("."));A.push(R),D.forEach(R,function(B,G){(!(D.isUndefined(B)||B===null)&&o.call(i,B,D.isString(G)?G.trim():G,z,O))===!0&&k(B,z?z.concat(G):[G])}),A.pop()}}if(!D.isObject(a))throw new TypeError("data must be an object");return k(a),i}function Tm(a){const i={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(a).replace(/[!'()~]|%20|%00/g,function(s){return i[s]})}function eo(a,i){this._pairs=[],a&&lr(a,this,i)}const Tp=eo.prototype;Tp.append=function(i,c){this._pairs.push([i,c])};Tp.toString=function(i){const c=i?function(s){return i.call(this,s,Tm)}:Tm;return this._pairs.map(function(o){return c(o[0])+"="+c(o[1])},"").join("&")};function N1(a){return encodeURIComponent(a).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ap(a,i,c){if(!i)return a;const s=c&&c.encode||N1;D.isFunction(c)&&(c={serialize:c});const o=c&&c.serialize;let f;if(o?f=o(i,c):f=D.isURLSearchParams(i)?i.toString():new eo(i,c).toString(s),f){const h=a.indexOf("#");h!==-1&&(a=a.slice(0,h)),a+=(a.indexOf("?")===-1?"?":"&")+f}return a}class Am{constructor(){this.handlers=[]}use(i,c,s){return this.handlers.push({fulfilled:i,rejected:c,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(i){this.handlers[i]&&(this.handlers[i]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(i){D.forEach(this.handlers,function(s){s!==null&&i(s)})}}const wp={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},_1=typeof URLSearchParams<"u"?URLSearchParams:eo,z1=typeof FormData<"u"?FormData:null,C1=typeof Blob<"u"?Blob:null,M1={isBrowser:!0,classes:{URLSearchParams:_1,FormData:z1,Blob:C1},protocols:["http","https","file","blob","url","data"]},to=typeof window<"u"&&typeof document<"u",qc=typeof navigator=="object"&&navigator||void 0,D1=to&&(!qc||["ReactNative","NativeScript","NS"].indexOf(qc.product)<0),U1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",j1=to&&window.location.href||"http://localhost",H1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:to,hasStandardBrowserEnv:D1,hasStandardBrowserWebWorkerEnv:U1,navigator:qc,origin:j1},Symbol.toStringTag,{value:"Module"})),at={...H1,...M1};function B1(a,i){return lr(a,new at.classes.URLSearchParams,Object.assign({visitor:function(c,s,o,f){return at.isNode&&D.isBuffer(c)?(this.append(s,c.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},i))}function L1(a){return D.matchAll(/\w+|\[(\w*)]/g,a).map(i=>i[0]==="[]"?"":i[1]||i[0])}function q1(a){const i={},c=Object.keys(a);let s;const o=c.length;let f;for(s=0;s<o;s++)f=c[s],i[f]=a[f];return i}function Op(a){function i(c,s,o,f){let h=c[f++];if(h==="__proto__")return!0;const g=Number.isFinite(+h),y=f>=c.length;return h=!h&&D.isArray(o)?o.length:h,y?(D.hasOwnProp(o,h)?o[h]=[o[h],s]:o[h]=s,!g):((!o[h]||!D.isObject(o[h]))&&(o[h]=[]),i(c,s,o[h],f)&&D.isArray(o[h])&&(o[h]=q1(o[h])),!g)}if(D.isFormData(a)&&D.isFunction(a.entries)){const c={};return D.forEachEntry(a,(s,o)=>{i(L1(s),o,c,0)}),c}return null}function k1(a,i,c){if(D.isString(a))try{return(i||JSON.parse)(a),D.trim(a)}catch(s){if(s.name!=="SyntaxError")throw s}return(c||JSON.stringify)(a)}const xu={transitional:wp,adapter:["xhr","http","fetch"],transformRequest:[function(i,c){const s=c.getContentType()||"",o=s.indexOf("application/json")>-1,f=D.isObject(i);if(f&&D.isHTMLForm(i)&&(i=new FormData(i)),D.isFormData(i))return o?JSON.stringify(Op(i)):i;if(D.isArrayBuffer(i)||D.isBuffer(i)||D.isStream(i)||D.isFile(i)||D.isBlob(i)||D.isReadableStream(i))return i;if(D.isArrayBufferView(i))return i.buffer;if(D.isURLSearchParams(i))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),i.toString();let g;if(f){if(s.indexOf("application/x-www-form-urlencoded")>-1)return B1(i,this.formSerializer).toString();if((g=D.isFileList(i))||s.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return lr(g?{"files[]":i}:i,y&&new y,this.formSerializer)}}return f||o?(c.setContentType("application/json",!1),k1(i)):i}],transformResponse:[function(i){const c=this.transitional||xu.transitional,s=c&&c.forcedJSONParsing,o=this.responseType==="json";if(D.isResponse(i)||D.isReadableStream(i))return i;if(i&&D.isString(i)&&(s&&!this.responseType||o)){const h=!(c&&c.silentJSONParsing)&&o;try{return JSON.parse(i)}catch(g){if(h)throw g.name==="SyntaxError"?re.from(g,re.ERR_BAD_RESPONSE,this,null,this.response):g}}return i}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(i){return i>=200&&i<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],a=>{xu.headers[a]={}});const Y1=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),G1=a=>{const i={};let c,s,o;return a&&a.split(`
`).forEach(function(h){o=h.indexOf(":"),c=h.substring(0,o).trim().toLowerCase(),s=h.substring(o+1).trim(),!(!c||i[c]&&Y1[c])&&(c==="set-cookie"?i[c]?i[c].push(s):i[c]=[s]:i[c]=i[c]?i[c]+", "+s:s)}),i},wm=Symbol("internals");function du(a){return a&&String(a).trim().toLowerCase()}function Gi(a){return a===!1||a==null?a:D.isArray(a)?a.map(Gi):String(a)}function V1(a){const i=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=c.exec(a);)i[s[1]]=s[2];return i}const X1=a=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(a.trim());function Nc(a,i,c,s,o){if(D.isFunction(s))return s.call(this,i,c);if(o&&(i=c),!!D.isString(i)){if(D.isString(s))return i.indexOf(s)!==-1;if(D.isRegExp(s))return s.test(i)}}function Q1(a){return a.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(i,c,s)=>c.toUpperCase()+s)}function Z1(a,i){const c=D.toCamelCase(" "+i);["get","set","has"].forEach(s=>{Object.defineProperty(a,s+c,{value:function(o,f,h){return this[s].call(this,i,o,f,h)},configurable:!0})})}let mt=class{constructor(i){i&&this.set(i)}set(i,c,s){const o=this;function f(g,y,m){const v=du(y);if(!v)throw new Error("header name must be a non-empty string");const A=D.findKey(o,v);(!A||o[A]===void 0||m===!0||m===void 0&&o[A]!==!1)&&(o[A||y]=Gi(g))}const h=(g,y)=>D.forEach(g,(m,v)=>f(m,v,y));if(D.isPlainObject(i)||i instanceof this.constructor)h(i,c);else if(D.isString(i)&&(i=i.trim())&&!X1(i))h(G1(i),c);else if(D.isObject(i)&&D.isIterable(i)){let g={},y,m;for(const v of i){if(!D.isArray(v))throw TypeError("Object iterator must return a key-value pair");g[m=v[0]]=(y=g[m])?D.isArray(y)?[...y,v[1]]:[y,v[1]]:v[1]}h(g,c)}else i!=null&&f(c,i,s);return this}get(i,c){if(i=du(i),i){const s=D.findKey(this,i);if(s){const o=this[s];if(!c)return o;if(c===!0)return V1(o);if(D.isFunction(c))return c.call(this,o,s);if(D.isRegExp(c))return c.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(i,c){if(i=du(i),i){const s=D.findKey(this,i);return!!(s&&this[s]!==void 0&&(!c||Nc(this,this[s],s,c)))}return!1}delete(i,c){const s=this;let o=!1;function f(h){if(h=du(h),h){const g=D.findKey(s,h);g&&(!c||Nc(s,s[g],g,c))&&(delete s[g],o=!0)}}return D.isArray(i)?i.forEach(f):f(i),o}clear(i){const c=Object.keys(this);let s=c.length,o=!1;for(;s--;){const f=c[s];(!i||Nc(this,this[f],f,i,!0))&&(delete this[f],o=!0)}return o}normalize(i){const c=this,s={};return D.forEach(this,(o,f)=>{const h=D.findKey(s,f);if(h){c[h]=Gi(o),delete c[f];return}const g=i?Q1(f):String(f).trim();g!==f&&delete c[f],c[g]=Gi(o),s[g]=!0}),this}concat(...i){return this.constructor.concat(this,...i)}toJSON(i){const c=Object.create(null);return D.forEach(this,(s,o)=>{s!=null&&s!==!1&&(c[o]=i&&D.isArray(s)?s.join(", "):s)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([i,c])=>i+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(i){return i instanceof this?i:new this(i)}static concat(i,...c){const s=new this(i);return c.forEach(o=>s.set(o)),s}static accessor(i){const s=(this[wm]=this[wm]={accessors:{}}).accessors,o=this.prototype;function f(h){const g=du(h);s[g]||(Z1(o,h),s[g]=!0)}return D.isArray(i)?i.forEach(f):f(i),this}};mt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(mt.prototype,({value:a},i)=>{let c=i[0].toUpperCase()+i.slice(1);return{get:()=>a,set(s){this[c]=s}}});D.freezeMethods(mt);function _c(a,i){const c=this||xu,s=i||c,o=mt.from(s.headers);let f=s.data;return D.forEach(a,function(g){f=g.call(c,f,o.normalize(),i?i.status:void 0)}),o.normalize(),f}function Np(a){return!!(a&&a.__CANCEL__)}function ca(a,i,c){re.call(this,a??"canceled",re.ERR_CANCELED,i,c),this.name="CanceledError"}D.inherits(ca,re,{__CANCEL__:!0});function _p(a,i,c){const s=c.config.validateStatus;!c.status||!s||s(c.status)?a(c):i(new re("Request failed with status code "+c.status,[re.ERR_BAD_REQUEST,re.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function K1(a){const i=/^([-+\w]{1,25})(:?\/\/|:)/.exec(a);return i&&i[1]||""}function J1(a,i){a=a||10;const c=new Array(a),s=new Array(a);let o=0,f=0,h;return i=i!==void 0?i:1e3,function(y){const m=Date.now(),v=s[f];h||(h=m),c[o]=y,s[o]=m;let A=f,O=0;for(;A!==o;)O+=c[A++],A=A%a;if(o=(o+1)%a,o===f&&(f=(f+1)%a),m-h<i)return;const k=v&&m-v;return k?Math.round(O*1e3/k):void 0}}function $1(a,i){let c=0,s=1e3/i,o,f;const h=(m,v=Date.now())=>{c=v,o=null,f&&(clearTimeout(f),f=null),a.apply(null,m)};return[(...m)=>{const v=Date.now(),A=v-c;A>=s?h(m,v):(o=m,f||(f=setTimeout(()=>{f=null,h(o)},s-A)))},()=>o&&h(o)]}const Zi=(a,i,c=3)=>{let s=0;const o=J1(50,250);return $1(f=>{const h=f.loaded,g=f.lengthComputable?f.total:void 0,y=h-s,m=o(y),v=h<=g;s=h;const A={loaded:h,total:g,progress:g?h/g:void 0,bytes:y,rate:m||void 0,estimated:m&&g&&v?(g-h)/m:void 0,event:f,lengthComputable:g!=null,[i?"download":"upload"]:!0};a(A)},c)},Om=(a,i)=>{const c=a!=null;return[s=>i[0]({lengthComputable:c,total:a,loaded:s}),i[1]]},Nm=a=>(...i)=>D.asap(()=>a(...i)),F1=at.hasStandardBrowserEnv?((a,i)=>c=>(c=new URL(c,at.origin),a.protocol===c.protocol&&a.host===c.host&&(i||a.port===c.port)))(new URL(at.origin),at.navigator&&/(msie|trident)/i.test(at.navigator.userAgent)):()=>!0,W1=at.hasStandardBrowserEnv?{write(a,i,c,s,o,f){const h=[a+"="+encodeURIComponent(i)];D.isNumber(c)&&h.push("expires="+new Date(c).toGMTString()),D.isString(s)&&h.push("path="+s),D.isString(o)&&h.push("domain="+o),f===!0&&h.push("secure"),document.cookie=h.join("; ")},read(a){const i=document.cookie.match(new RegExp("(^|;\\s*)("+a+")=([^;]*)"));return i?decodeURIComponent(i[3]):null},remove(a){this.write(a,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function P1(a){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)}function I1(a,i){return i?a.replace(/\/?\/$/,"")+"/"+i.replace(/^\/+/,""):a}function zp(a,i,c){let s=!P1(i);return a&&(s||c==!1)?I1(a,i):i}const _m=a=>a instanceof mt?{...a}:a;function hn(a,i){i=i||{};const c={};function s(m,v,A,O){return D.isPlainObject(m)&&D.isPlainObject(v)?D.merge.call({caseless:O},m,v):D.isPlainObject(v)?D.merge({},v):D.isArray(v)?v.slice():v}function o(m,v,A,O){if(D.isUndefined(v)){if(!D.isUndefined(m))return s(void 0,m,A,O)}else return s(m,v,A,O)}function f(m,v){if(!D.isUndefined(v))return s(void 0,v)}function h(m,v){if(D.isUndefined(v)){if(!D.isUndefined(m))return s(void 0,m)}else return s(void 0,v)}function g(m,v,A){if(A in i)return s(m,v);if(A in a)return s(void 0,m)}const y={url:f,method:f,data:f,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:g,headers:(m,v,A)=>o(_m(m),_m(v),A,!0)};return D.forEach(Object.keys(Object.assign({},a,i)),function(v){const A=y[v]||o,O=A(a[v],i[v],v);D.isUndefined(O)&&A!==g||(c[v]=O)}),c}const Cp=a=>{const i=hn({},a);let{data:c,withXSRFToken:s,xsrfHeaderName:o,xsrfCookieName:f,headers:h,auth:g}=i;i.headers=h=mt.from(h),i.url=Ap(zp(i.baseURL,i.url,i.allowAbsoluteUrls),a.params,a.paramsSerializer),g&&h.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let y;if(D.isFormData(c)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((y=h.getContentType())!==!1){const[m,...v]=y?y.split(";").map(A=>A.trim()).filter(Boolean):[];h.setContentType([m||"multipart/form-data",...v].join("; "))}}if(at.hasStandardBrowserEnv&&(s&&D.isFunction(s)&&(s=s(i)),s||s!==!1&&F1(i.url))){const m=o&&f&&W1.read(f);m&&h.set(o,m)}return i},eS=typeof XMLHttpRequest<"u",tS=eS&&function(a){return new Promise(function(c,s){const o=Cp(a);let f=o.data;const h=mt.from(o.headers).normalize();let{responseType:g,onUploadProgress:y,onDownloadProgress:m}=o,v,A,O,k,R;function z(){k&&k(),R&&R(),o.cancelToken&&o.cancelToken.unsubscribe(v),o.signal&&o.signal.removeEventListener("abort",v)}let U=new XMLHttpRequest;U.open(o.method.toUpperCase(),o.url,!0),U.timeout=o.timeout;function B(){if(!U)return;const Q=mt.from("getAllResponseHeaders"in U&&U.getAllResponseHeaders()),K={data:!g||g==="text"||g==="json"?U.responseText:U.response,status:U.status,statusText:U.statusText,headers:Q,config:a,request:U};_p(function(J){c(J),z()},function(J){s(J),z()},K),U=null}"onloadend"in U?U.onloadend=B:U.onreadystatechange=function(){!U||U.readyState!==4||U.status===0&&!(U.responseURL&&U.responseURL.indexOf("file:")===0)||setTimeout(B)},U.onabort=function(){U&&(s(new re("Request aborted",re.ECONNABORTED,a,U)),U=null)},U.onerror=function(){s(new re("Network Error",re.ERR_NETWORK,a,U)),U=null},U.ontimeout=function(){let le=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const K=o.transitional||wp;o.timeoutErrorMessage&&(le=o.timeoutErrorMessage),s(new re(le,K.clarifyTimeoutError?re.ETIMEDOUT:re.ECONNABORTED,a,U)),U=null},f===void 0&&h.setContentType(null),"setRequestHeader"in U&&D.forEach(h.toJSON(),function(le,K){U.setRequestHeader(K,le)}),D.isUndefined(o.withCredentials)||(U.withCredentials=!!o.withCredentials),g&&g!=="json"&&(U.responseType=o.responseType),m&&([O,R]=Zi(m,!0),U.addEventListener("progress",O)),y&&U.upload&&([A,k]=Zi(y),U.upload.addEventListener("progress",A),U.upload.addEventListener("loadend",k)),(o.cancelToken||o.signal)&&(v=Q=>{U&&(s(!Q||Q.type?new ca(null,a,U):Q),U.abort(),U=null)},o.cancelToken&&o.cancelToken.subscribe(v),o.signal&&(o.signal.aborted?v():o.signal.addEventListener("abort",v)));const G=K1(o.url);if(G&&at.protocols.indexOf(G)===-1){s(new re("Unsupported protocol "+G+":",re.ERR_BAD_REQUEST,a));return}U.send(f||null)})},lS=(a,i)=>{const{length:c}=a=a?a.filter(Boolean):[];if(i||c){let s=new AbortController,o;const f=function(m){if(!o){o=!0,g();const v=m instanceof Error?m:this.reason;s.abort(v instanceof re?v:new ca(v instanceof Error?v.message:v))}};let h=i&&setTimeout(()=>{h=null,f(new re(`timeout ${i} of ms exceeded`,re.ETIMEDOUT))},i);const g=()=>{a&&(h&&clearTimeout(h),h=null,a.forEach(m=>{m.unsubscribe?m.unsubscribe(f):m.removeEventListener("abort",f)}),a=null)};a.forEach(m=>m.addEventListener("abort",f));const{signal:y}=s;return y.unsubscribe=()=>D.asap(g),y}},nS=function*(a,i){let c=a.byteLength;if(c<i){yield a;return}let s=0,o;for(;s<c;)o=s+i,yield a.slice(s,o),s=o},aS=async function*(a,i){for await(const c of uS(a))yield*nS(c,i)},uS=async function*(a){if(a[Symbol.asyncIterator]){yield*a;return}const i=a.getReader();try{for(;;){const{done:c,value:s}=await i.read();if(c)break;yield s}}finally{await i.cancel()}},zm=(a,i,c,s)=>{const o=aS(a,i);let f=0,h,g=y=>{h||(h=!0,s&&s(y))};return new ReadableStream({async pull(y){try{const{done:m,value:v}=await o.next();if(m){g(),y.close();return}let A=v.byteLength;if(c){let O=f+=A;c(O)}y.enqueue(new Uint8Array(v))}catch(m){throw g(m),m}},cancel(y){return g(y),o.return()}},{highWaterMark:2})},nr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Mp=nr&&typeof ReadableStream=="function",iS=nr&&(typeof TextEncoder=="function"?(a=>i=>a.encode(i))(new TextEncoder):async a=>new Uint8Array(await new Response(a).arrayBuffer())),Dp=(a,...i)=>{try{return!!a(...i)}catch{return!1}},rS=Mp&&Dp(()=>{let a=!1;const i=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return a=!0,"half"}}).headers.has("Content-Type");return a&&!i}),Cm=64*1024,kc=Mp&&Dp(()=>D.isReadableStream(new Response("").body)),Ki={stream:kc&&(a=>a.body)};nr&&(a=>{["text","arrayBuffer","blob","formData","stream"].forEach(i=>{!Ki[i]&&(Ki[i]=D.isFunction(a[i])?c=>c[i]():(c,s)=>{throw new re(`Response type '${i}' is not supported`,re.ERR_NOT_SUPPORT,s)})})})(new Response);const sS=async a=>{if(a==null)return 0;if(D.isBlob(a))return a.size;if(D.isSpecCompliantForm(a))return(await new Request(at.origin,{method:"POST",body:a}).arrayBuffer()).byteLength;if(D.isArrayBufferView(a)||D.isArrayBuffer(a))return a.byteLength;if(D.isURLSearchParams(a)&&(a=a+""),D.isString(a))return(await iS(a)).byteLength},cS=async(a,i)=>{const c=D.toFiniteNumber(a.getContentLength());return c??sS(i)},oS=nr&&(async a=>{let{url:i,method:c,data:s,signal:o,cancelToken:f,timeout:h,onDownloadProgress:g,onUploadProgress:y,responseType:m,headers:v,withCredentials:A="same-origin",fetchOptions:O}=Cp(a);m=m?(m+"").toLowerCase():"text";let k=lS([o,f&&f.toAbortSignal()],h),R;const z=k&&k.unsubscribe&&(()=>{k.unsubscribe()});let U;try{if(y&&rS&&c!=="get"&&c!=="head"&&(U=await cS(v,s))!==0){let K=new Request(i,{method:"POST",body:s,duplex:"half"}),ce;if(D.isFormData(s)&&(ce=K.headers.get("content-type"))&&v.setContentType(ce),K.body){const[J,$]=Om(U,Zi(Nm(y)));s=zm(K.body,Cm,J,$)}}D.isString(A)||(A=A?"include":"omit");const B="credentials"in Request.prototype;R=new Request(i,{...O,signal:k,method:c.toUpperCase(),headers:v.normalize().toJSON(),body:s,duplex:"half",credentials:B?A:void 0});let G=await fetch(R);const Q=kc&&(m==="stream"||m==="response");if(kc&&(g||Q&&z)){const K={};["status","statusText","headers"].forEach(he=>{K[he]=G[he]});const ce=D.toFiniteNumber(G.headers.get("content-length")),[J,$]=g&&Om(ce,Zi(Nm(g),!0))||[];G=new Response(zm(G.body,Cm,J,()=>{$&&$(),z&&z()}),K)}m=m||"text";let le=await Ki[D.findKey(Ki,m)||"text"](G,a);return!Q&&z&&z(),await new Promise((K,ce)=>{_p(K,ce,{data:le,headers:mt.from(G.headers),status:G.status,statusText:G.statusText,config:a,request:R})})}catch(B){throw z&&z(),B&&B.name==="TypeError"&&/Load failed|fetch/i.test(B.message)?Object.assign(new re("Network Error",re.ERR_NETWORK,a,R),{cause:B.cause||B}):re.from(B,B&&B.code,a,R)}}),Yc={http:A1,xhr:tS,fetch:oS};D.forEach(Yc,(a,i)=>{if(a){try{Object.defineProperty(a,"name",{value:i})}catch{}Object.defineProperty(a,"adapterName",{value:i})}});const Mm=a=>`- ${a}`,fS=a=>D.isFunction(a)||a===null||a===!1,Up={getAdapter:a=>{a=D.isArray(a)?a:[a];const{length:i}=a;let c,s;const o={};for(let f=0;f<i;f++){c=a[f];let h;if(s=c,!fS(c)&&(s=Yc[(h=String(c)).toLowerCase()],s===void 0))throw new re(`Unknown adapter '${h}'`);if(s)break;o[h||"#"+f]=s}if(!s){const f=Object.entries(o).map(([g,y])=>`adapter ${g} `+(y===!1?"is not supported by the environment":"is not available in the build"));let h=i?f.length>1?`since :
`+f.map(Mm).join(`
`):" "+Mm(f[0]):"as no adapter specified";throw new re("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return s},adapters:Yc};function zc(a){if(a.cancelToken&&a.cancelToken.throwIfRequested(),a.signal&&a.signal.aborted)throw new ca(null,a)}function Dm(a){return zc(a),a.headers=mt.from(a.headers),a.data=_c.call(a,a.transformRequest),["post","put","patch"].indexOf(a.method)!==-1&&a.headers.setContentType("application/x-www-form-urlencoded",!1),Up.getAdapter(a.adapter||xu.adapter)(a).then(function(s){return zc(a),s.data=_c.call(a,a.transformResponse,s),s.headers=mt.from(s.headers),s},function(s){return Np(s)||(zc(a),s&&s.response&&(s.response.data=_c.call(a,a.transformResponse,s.response),s.response.headers=mt.from(s.response.headers))),Promise.reject(s)})}const jp="1.9.0",ar={};["object","boolean","number","function","string","symbol"].forEach((a,i)=>{ar[a]=function(s){return typeof s===a||"a"+(i<1?"n ":" ")+a}});const Um={};ar.transitional=function(i,c,s){function o(f,h){return"[Axios v"+jp+"] Transitional option '"+f+"'"+h+(s?". "+s:"")}return(f,h,g)=>{if(i===!1)throw new re(o(h," has been removed"+(c?" in "+c:"")),re.ERR_DEPRECATED);return c&&!Um[h]&&(Um[h]=!0,console.warn(o(h," has been deprecated since v"+c+" and will be removed in the near future"))),i?i(f,h,g):!0}};ar.spelling=function(i){return(c,s)=>(console.warn(`${s} is likely a misspelling of ${i}`),!0)};function dS(a,i,c){if(typeof a!="object")throw new re("options must be an object",re.ERR_BAD_OPTION_VALUE);const s=Object.keys(a);let o=s.length;for(;o-- >0;){const f=s[o],h=i[f];if(h){const g=a[f],y=g===void 0||h(g,f,a);if(y!==!0)throw new re("option "+f+" must be "+y,re.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new re("Unknown option "+f,re.ERR_BAD_OPTION)}}const Vi={assertOptions:dS,validators:ar},Jt=Vi.validators;let dn=class{constructor(i){this.defaults=i||{},this.interceptors={request:new Am,response:new Am}}async request(i,c){try{return await this._request(i,c)}catch(s){if(s instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const f=o.stack?o.stack.replace(/^.+\n/,""):"";try{s.stack?f&&!String(s.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+f):s.stack=f}catch{}}throw s}}_request(i,c){typeof i=="string"?(c=c||{},c.url=i):c=i||{},c=hn(this.defaults,c);const{transitional:s,paramsSerializer:o,headers:f}=c;s!==void 0&&Vi.assertOptions(s,{silentJSONParsing:Jt.transitional(Jt.boolean),forcedJSONParsing:Jt.transitional(Jt.boolean),clarifyTimeoutError:Jt.transitional(Jt.boolean)},!1),o!=null&&(D.isFunction(o)?c.paramsSerializer={serialize:o}:Vi.assertOptions(o,{encode:Jt.function,serialize:Jt.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),Vi.assertOptions(c,{baseUrl:Jt.spelling("baseURL"),withXsrfToken:Jt.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let h=f&&D.merge(f.common,f[c.method]);f&&D.forEach(["delete","get","head","post","put","patch","common"],R=>{delete f[R]}),c.headers=mt.concat(h,f);const g=[];let y=!0;this.interceptors.request.forEach(function(z){typeof z.runWhen=="function"&&z.runWhen(c)===!1||(y=y&&z.synchronous,g.unshift(z.fulfilled,z.rejected))});const m=[];this.interceptors.response.forEach(function(z){m.push(z.fulfilled,z.rejected)});let v,A=0,O;if(!y){const R=[Dm.bind(this),void 0];for(R.unshift.apply(R,g),R.push.apply(R,m),O=R.length,v=Promise.resolve(c);A<O;)v=v.then(R[A++],R[A++]);return v}O=g.length;let k=c;for(A=0;A<O;){const R=g[A++],z=g[A++];try{k=R(k)}catch(U){z.call(this,U);break}}try{v=Dm.call(this,k)}catch(R){return Promise.reject(R)}for(A=0,O=m.length;A<O;)v=v.then(m[A++],m[A++]);return v}getUri(i){i=hn(this.defaults,i);const c=zp(i.baseURL,i.url,i.allowAbsoluteUrls);return Ap(c,i.params,i.paramsSerializer)}};D.forEach(["delete","get","head","options"],function(i){dn.prototype[i]=function(c,s){return this.request(hn(s||{},{method:i,url:c,data:(s||{}).data}))}});D.forEach(["post","put","patch"],function(i){function c(s){return function(f,h,g){return this.request(hn(g||{},{method:i,headers:s?{"Content-Type":"multipart/form-data"}:{},url:f,data:h}))}}dn.prototype[i]=c(),dn.prototype[i+"Form"]=c(!0)});let hS=class Hp{constructor(i){if(typeof i!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(f){c=f});const s=this;this.promise.then(o=>{if(!s._listeners)return;let f=s._listeners.length;for(;f-- >0;)s._listeners[f](o);s._listeners=null}),this.promise.then=o=>{let f;const h=new Promise(g=>{s.subscribe(g),f=g}).then(o);return h.cancel=function(){s.unsubscribe(f)},h},i(function(f,h,g){s.reason||(s.reason=new ca(f,h,g),c(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(i){if(this.reason){i(this.reason);return}this._listeners?this._listeners.push(i):this._listeners=[i]}unsubscribe(i){if(!this._listeners)return;const c=this._listeners.indexOf(i);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const i=new AbortController,c=s=>{i.abort(s)};return this.subscribe(c),i.signal.unsubscribe=()=>this.unsubscribe(c),i.signal}static source(){let i;return{token:new Hp(function(o){i=o}),cancel:i}}};function mS(a){return function(c){return a.apply(null,c)}}function pS(a){return D.isObject(a)&&a.isAxiosError===!0}const Gc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Gc).forEach(([a,i])=>{Gc[i]=a});function Bp(a){const i=new dn(a),c=hp(dn.prototype.request,i);return D.extend(c,dn.prototype,i,{allOwnKeys:!0}),D.extend(c,i,null,{allOwnKeys:!0}),c.create=function(o){return Bp(hn(a,o))},c}const qe=Bp(xu);qe.Axios=dn;qe.CanceledError=ca;qe.CancelToken=hS;qe.isCancel=Np;qe.VERSION=jp;qe.toFormData=lr;qe.AxiosError=re;qe.Cancel=qe.CanceledError;qe.all=function(i){return Promise.all(i)};qe.spread=mS;qe.isAxiosError=pS;qe.mergeConfig=hn;qe.AxiosHeaders=mt;qe.formToJSON=a=>Op(D.isHTMLForm(a)?new FormData(a):a);qe.getAdapter=Up.getAdapter;qe.HttpStatusCode=Gc;qe.default=qe;const{Axios:o2,AxiosError:f2,CanceledError:d2,isCancel:h2,CancelToken:m2,VERSION:p2,all:y2,Cancel:g2,isAxiosError:v2,spread:b2,toFormData:S2,AxiosHeaders:x2,HttpStatusCode:E2,formToJSON:R2,getAdapter:T2,mergeConfig:A2}=qe,yS="http://localhost:3001/api",nt=qe.create({baseURL:yS,headers:{"Content-Type":"application/json"}});nt.interceptors.request.use(a=>{const i=localStorage.getItem("smartpos_token");return i&&(a.headers.Authorization=`Bearer ${i}`),a},a=>Promise.reject(a));nt.interceptors.response.use(a=>a,a=>{var i;return((i=a.response)==null?void 0:i.status)===401&&(localStorage.removeItem("smartpos_token"),localStorage.removeItem("smartpos_user"),window.location.href="/login"),Promise.reject(a)});const gS={login:async a=>(await nt.post("/auth/login",a)).data,register:async a=>(await nt.post("/auth/register",a)).data,getProfile:async()=>(await nt.get("/auth/profile")).data,updateProfile:async a=>(await nt.put("/auth/profile",a)).data,changePassword:async a=>(await nt.put("/auth/change-password",a)).data,logout:async()=>(await nt.post("/auth/logout")).data},jm={getAll:async a=>(await nt.get("/products",{params:a})).data,getById:async a=>(await nt.get(`/products/${a}`)).data,getByBarcode:async a=>(await nt.get(`/products/barcode/${a}`)).data,create:async a=>(await nt.post("/products",a)).data,update:async(a,i)=>(await nt.put(`/products/${a}`,i)).data,delete:async a=>(await nt.delete(`/products/${a}`)).data,updateStock:async(a,i,c)=>(await nt.patch(`/products/${a}/stock`,{quantity:i,operation:c})).data,getLowStock:async()=>(await nt.get("/products/low-stock")).data};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vS=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),bS=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,c,s)=>s?s.toUpperCase():c.toLowerCase()),Hm=a=>{const i=bS(a);return i.charAt(0).toUpperCase()+i.slice(1)},Lp=(...a)=>a.filter((i,c,s)=>!!i&&i.trim()!==""&&s.indexOf(i)===c).join(" ").trim(),SS=a=>{for(const i in a)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var xS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ES=M.forwardRef(({color:a="currentColor",size:i=24,strokeWidth:c=2,absoluteStrokeWidth:s,className:o="",children:f,iconNode:h,...g},y)=>M.createElement("svg",{ref:y,...xS,width:i,height:i,stroke:a,strokeWidth:s?Number(c)*24/Number(i):c,className:Lp("lucide",o),...!f&&!SS(g)&&{"aria-hidden":"true"},...g},[...h.map(([m,v])=>M.createElement(m,v)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=(a,i)=>{const c=M.forwardRef(({className:s,...o},f)=>M.createElement(ES,{ref:f,iconNode:i,className:Lp(`lucide-${vS(Hm(a))}`,`lucide-${a}`,s),...o}));return c.displayName=Hm(a),c};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RS=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],TS=Fe("arrow-left",RS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AS=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],wS=Fe("chart-column",AS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OS=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],NS=Fe("credit-card",OS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _S=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],zS=Fe("dollar-sign",_S);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CS=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],MS=Fe("lock",CS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DS=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],US=Fe("log-out",DS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jS=[["path",{d:"M5 12h14",key:"1ays0h"}]],HS=Fe("minus",jS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BS=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]],Bm=Fe("package",BS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LS=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],qS=Fe("plus",LS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kS=[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]],YS=Fe("scan",kS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GS=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],VS=Fe("search",GS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XS=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],QS=Fe("settings",XS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZS=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],Gl=Fe("shopping-cart",ZS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KS=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],JS=Fe("trash-2",KS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $S=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],FS=Fe("trending-up",$S);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WS=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],PS=Fe("triangle-alert",WS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IS=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],qp=Fe("user",IS);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e2=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],t2=Fe("users",e2),l2=()=>{const[a,i]=M.useState(""),[c,s]=M.useState(""),[o,f]=M.useState(!1),[h,g]=M.useState(""),{login:y}=bu(),m=gu(),v=async A=>{var O,k;A.preventDefault(),g(""),f(!0);try{const R=await gS.login({username:a,password:c});y(R.user,R.token),m("/dashboard")}catch(R){g(((k=(O=R.response)==null?void 0:O.data)==null?void 0:k.error)||"حدث خطأ أثناء تسجيل الدخول")}finally{f(!1)}};return E.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:E.jsxs("div",{className:"w-full max-w-md",children:[E.jsxs("div",{className:"text-center mb-8",children:[E.jsx("div",{className:"flex justify-center mb-4",children:E.jsx("div",{className:"bg-primary rounded-full p-3",children:E.jsx(Gl,{className:"h-8 w-8 text-primary-foreground"})})}),E.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SmartPOS"}),E.jsx("p",{className:"text-gray-600",children:"نظام الكاشير الذكي"})]}),E.jsxs(ta,{children:[E.jsxs(Fi,{children:[E.jsx(Wi,{className:"text-center",children:"تسجيل الدخول"}),E.jsx(Pc,{className:"text-center",children:"أدخل بياناتك للوصول إلى النظام"})]}),E.jsxs(la,{children:[E.jsxs("form",{onSubmit:v,className:"space-y-4",children:[h&&E.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:h}),E.jsxs("div",{className:"space-y-2",children:[E.jsx("label",{htmlFor:"username",className:"text-sm font-medium text-gray-700",children:"اسم المستخدم"}),E.jsxs("div",{className:"relative",children:[E.jsx(qp,{className:"absolute right-3 top-3 h-4 w-4 text-gray-400"}),E.jsx(mu,{id:"username",type:"text",value:a,onChange:A=>i(A.target.value),placeholder:"أدخل اسم المستخدم",className:"pr-10",required:!0,disabled:o})]})]}),E.jsxs("div",{className:"space-y-2",children:[E.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"كلمة المرور"}),E.jsxs("div",{className:"relative",children:[E.jsx(MS,{className:"absolute right-3 top-3 h-4 w-4 text-gray-400"}),E.jsx(mu,{id:"password",type:"password",value:c,onChange:A=>s(A.target.value),placeholder:"أدخل كلمة المرور",className:"pr-10",required:!0,disabled:o})]})]}),E.jsx(rt,{type:"submit",className:"w-full",disabled:o||!a||!c,children:o?"جاري تسجيل الدخول...":"تسجيل الدخول"})]}),E.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-md",children:[E.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"بيانات تجريبية:"}),E.jsxs("div",{className:"text-xs text-gray-600 space-y-1",children:[E.jsxs("div",{children:[E.jsx("strong",{children:"مدير:"})," admin / admin123"]}),E.jsxs("div",{children:[E.jsx("strong",{children:"كاشير:"})," cashier1 / cashier123"]})]})]})]})]}),E.jsx("div",{className:"text-center mt-6 text-sm text-gray-500",children:E.jsx("p",{children:"© 2024 SmartPOS. جميع الحقوق محفوظة."})})]})})},n2=()=>{const{user:a,logout:i}=bu(),c=gu(),s=()=>{i(),c("/login")},f=[{title:"نقطة البيع",description:"إجراء عمليات البيع والكاشير",icon:Gl,path:"/pos",color:"bg-blue-500",roles:["admin","manager","cashier"]},{title:"إدارة المنتجات",description:"إضافة وتعديل المنتجات والمخزون",icon:Bm,path:"/products",color:"bg-green-500",roles:["admin","manager"]},{title:"إدارة العملاء",description:"إدارة بيانات العملاء والحسابات",icon:t2,path:"/customers",color:"bg-purple-500",roles:["admin","manager","cashier"]},{title:"التقارير",description:"تقارير المبيعات والأرباح",icon:wS,path:"/reports",color:"bg-orange-500",roles:["admin","manager"]},{title:"الإعدادات",description:"إعدادات النظام والمستخدمين",icon:QS,path:"/settings",color:"bg-gray-500",roles:["admin"]}].filter(g=>g.roles.includes((a==null?void 0:a.role)||"cashier")),h=[{title:"مبيعات اليوم",value:"2,450 ر.س",icon:zS,color:"text-green-600",bgColor:"bg-green-100"},{title:"عدد الفواتير",value:"24",icon:Gl,color:"text-blue-600",bgColor:"bg-blue-100"},{title:"نمو المبيعات",value:"+12%",icon:FS,color:"text-purple-600",bgColor:"bg-purple-100"},{title:"تنبيهات المخزون",value:"3",icon:PS,color:"text-red-600",bgColor:"bg-red-100"}];return E.jsxs("div",{className:"min-h-screen bg-gray-50",children:[E.jsx("header",{className:"bg-white shadow-sm border-b",children:E.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:E.jsxs("div",{className:"flex justify-between items-center h-16",children:[E.jsxs("div",{className:"flex items-center",children:[E.jsx(Gl,{className:"h-8 w-8 text-primary ml-3"}),E.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"SmartPOS"})]}),E.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[E.jsxs("div",{className:"text-sm text-gray-700",children:["مرحباً، ",E.jsx("span",{className:"font-medium",children:a==null?void 0:a.full_name})]}),E.jsxs(rt,{variant:"outline",size:"sm",onClick:s,className:"flex items-center",children:[E.jsx(US,{className:"h-4 w-4 ml-2"}),"تسجيل الخروج"]})]})]})})}),E.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[E.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:h.map((g,y)=>E.jsx(ta,{children:E.jsx(la,{className:"p-6",children:E.jsxs("div",{className:"flex items-center",children:[E.jsx("div",{className:`p-2 rounded-lg ${g.bgColor}`,children:E.jsx(g.icon,{className:`h-6 w-6 ${g.color}`})}),E.jsxs("div",{className:"mr-4",children:[E.jsx("p",{className:"text-sm font-medium text-gray-600",children:g.title}),E.jsx("p",{className:"text-2xl font-bold text-gray-900",children:g.value})]})]})})},y))}),E.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map((g,y)=>E.jsxs(ta,{className:"hover:shadow-lg transition-shadow cursor-pointer",onClick:()=>c(g.path),children:[E.jsx(Fi,{children:E.jsxs("div",{className:"flex items-center",children:[E.jsx("div",{className:`p-3 rounded-lg ${g.color} text-white`,children:E.jsx(g.icon,{className:"h-6 w-6"})}),E.jsx("div",{className:"mr-4",children:E.jsx(Wi,{className:"text-lg",children:g.title})})]})}),E.jsx(la,{children:E.jsx(Pc,{className:"text-gray-600",children:g.description})})]},y))}),E.jsxs("div",{className:"mt-8",children:[E.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"الوصول السريع"}),E.jsxs("div",{className:"flex flex-wrap gap-4",children:[E.jsxs(rt,{onClick:()=>c("/pos"),className:"flex items-center",children:[E.jsx(Gl,{className:"h-4 w-4 ml-2"}),"بدء عملية بيع جديدة"]}),((a==null?void 0:a.role)==="admin"||(a==null?void 0:a.role)==="manager")&&E.jsxs(rt,{variant:"outline",onClick:()=>c("/products/add"),className:"flex items-center",children:[E.jsx(Bm,{className:"h-4 w-4 ml-2"}),"إضافة منتج جديد"]})]})]})]})]})},a2=ep((a,i)=>({cart:[],customer:null,discount:0,discountType:"percentage",paymentMethod:"cash",isLoading:!1,error:null,addToCart:(c,s=1)=>{const{cart:o}=i();if(o.find(h=>h.product.id===c.id))a({cart:o.map(h=>h.product.id===c.id?{...h,quantity:h.quantity+s,total:(h.quantity+s)*h.price}:h)});else{const h={product:c,quantity:s,price:c.selling_price,total:c.selling_price*s};a({cart:[...o,h]})}},removeFromCart:c=>{const{cart:s}=i();a({cart:s.filter(o=>o.product.id!==c)})},updateQuantity:(c,s)=>{if(s<=0){i().removeFromCart(c);return}const{cart:o}=i();a({cart:o.map(f=>f.product.id===c?{...f,quantity:s,total:s*f.price}:f)})},clearCart:()=>{a({cart:[],customer:null,discount:0,discountType:"percentage",paymentMethod:"cash"})},setCustomer:c=>{a({customer:c})},setDiscount:(c,s)=>{a({discount:c,discountType:s})},setPaymentMethod:c=>{a({paymentMethod:c})},getSubtotal:()=>{const{cart:c}=i();return c.reduce((s,o)=>s+o.total,0)},getDiscountAmount:()=>{const{discount:c,discountType:s}=i(),o=i().getSubtotal();return s==="percentage"?o*c/100:Math.min(c,o)},getTotal:()=>{const c=i().getSubtotal(),s=i().getDiscountAmount();return Math.max(0,c-s)},setLoading:c=>{a({isLoading:c})},setError:c=>{a({error:c})},clearError:()=>{a({error:null})}})),u2=()=>{const[a,i]=M.useState([]),[c,s]=M.useState(""),[o,f]=M.useState(""),{user:h}=bu(),{cart:g,customer:y,discount:m,paymentMethod:v,addToCart:A,removeFromCart:O,updateQuantity:k,clearCart:R,getSubtotal:z,getDiscountAmount:U,getTotal:B}=a2(),G=gu();M.useEffect(()=>{Q()},[]);const Q=async()=>{try{const J=await jm.getAll();i(J.products||[])}catch(J){console.error("خطأ في تحميل المنتجات:",J)}},le=a.filter(J=>{var $;return J.name.toLowerCase().includes(c.toLowerCase())||(($=J.barcode)==null?void 0:$.includes(c))}),K=async()=>{if(o.trim())try{const J=await jm.getByBarcode(o);J&&(A(J),f(""))}catch{console.error("المنتج غير موجود")}},ce=()=>{g.length!==0&&(console.log("إتمام البيع:",{cart:g,customer:y,subtotal:z(),discount:U(),total:B(),paymentMethod:v}),R(),alert("تم إتمام عملية البيع بنجاح!"))};return E.jsxs("div",{className:"min-h-screen bg-gray-50",children:[E.jsx("header",{className:"bg-white shadow-sm border-b",children:E.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:E.jsxs("div",{className:"flex justify-between items-center h-16",children:[E.jsxs("div",{className:"flex items-center",children:[E.jsx(rt,{variant:"ghost",onClick:()=>G("/dashboard"),className:"ml-4",children:E.jsx(TS,{className:"h-4 w-4"})}),E.jsx(Gl,{className:"h-6 w-6 text-primary ml-3"}),E.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"نقطة البيع"})]}),E.jsxs("div",{className:"text-sm text-gray-700",children:["الكاشير: ",E.jsx("span",{className:"font-medium",children:h==null?void 0:h.full_name})]})]})})}),E.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:E.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[E.jsxs("div",{className:"lg:col-span-2",children:[E.jsxs("div",{className:"mb-6 space-y-4",children:[E.jsxs("div",{className:"flex gap-4",children:[E.jsx("div",{className:"flex-1",children:E.jsx(mu,{placeholder:"البحث في المنتجات...",value:c,onChange:J=>s(J.target.value),className:"w-full"})}),E.jsx(rt,{variant:"outline",children:E.jsx(VS,{className:"h-4 w-4"})})]}),E.jsxs("div",{className:"flex gap-4",children:[E.jsx("div",{className:"flex-1",children:E.jsx(mu,{placeholder:"مسح الباركود أو إدخاله يدوياً",value:o,onChange:J=>f(J.target.value),onKeyPress:J=>J.key==="Enter"&&K()})}),E.jsxs(rt,{onClick:K,children:[E.jsx(YS,{className:"h-4 w-4 ml-2"}),"مسح"]})]})]}),E.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:le.map(J=>E.jsx(ta,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>A(J),children:E.jsxs(la,{className:"p-4",children:[E.jsx("h3",{className:"font-medium text-sm mb-2 line-clamp-2",children:J.name}),E.jsxs("p",{className:"text-lg font-bold text-primary mb-1",children:[J.selling_price," ر.س"]}),E.jsxs("p",{className:"text-xs text-gray-500",children:["المخزون: ",J.stock_quantity," ",J.unit]}),J.category_name&&E.jsx("p",{className:"text-xs text-gray-400 mt-1",children:J.category_name})]})},J.id))})]}),E.jsx("div",{className:"lg:col-span-1",children:E.jsxs(ta,{className:"sticky top-6",children:[E.jsx(Fi,{children:E.jsxs(Wi,{className:"flex items-center",children:[E.jsx(Gl,{className:"h-5 w-5 ml-2"}),"سلة التسوق (",g.length,")"]})}),E.jsxs(la,{className:"space-y-4",children:[E.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[E.jsxs("div",{className:"flex items-center",children:[E.jsx(qp,{className:"h-4 w-4 ml-2"}),E.jsx("span",{className:"text-sm",children:y?y.name:"عميل عادي"})]}),E.jsx(rt,{variant:"outline",size:"sm",children:"تغيير"})]}),E.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:g.map(J=>E.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[E.jsxs("div",{className:"flex-1",children:[E.jsx("p",{className:"text-sm font-medium",children:J.product.name}),E.jsxs("p",{className:"text-xs text-gray-500",children:[J.price," ر.س × ",J.quantity]})]}),E.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[E.jsx(rt,{variant:"outline",size:"sm",onClick:()=>k(J.product.id,J.quantity-1),children:E.jsx(HS,{className:"h-3 w-3"})}),E.jsx("span",{className:"text-sm font-medium w-8 text-center",children:J.quantity}),E.jsx(rt,{variant:"outline",size:"sm",onClick:()=>k(J.product.id,J.quantity+1),children:E.jsx(qS,{className:"h-3 w-3"})}),E.jsx(rt,{variant:"destructive",size:"sm",onClick:()=>O(J.product.id),children:E.jsx(JS,{className:"h-3 w-3"})})]})]},J.product.id))}),g.length===0&&E.jsxs("div",{className:"text-center py-8 text-gray-500",children:[E.jsx(Gl,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),E.jsx("p",{children:"السلة فارغة"})]}),g.length>0&&E.jsxs("div",{className:"space-y-2 pt-4 border-t",children:[E.jsxs("div",{className:"flex justify-between text-sm",children:[E.jsx("span",{children:"المجموع الفرعي:"}),E.jsxs("span",{children:[z().toFixed(2)," ر.س"]})]}),m>0&&E.jsxs("div",{className:"flex justify-between text-sm text-red-600",children:[E.jsx("span",{children:"الخصم:"}),E.jsxs("span",{children:["-",U().toFixed(2)," ر.س"]})]}),E.jsxs("div",{className:"flex justify-between text-lg font-bold pt-2 border-t",children:[E.jsx("span",{children:"الإجمالي:"}),E.jsxs("span",{children:[B().toFixed(2)," ر.س"]})]})]}),E.jsxs("div",{className:"space-y-2 pt-4",children:[E.jsxs(rt,{className:"w-full",onClick:ce,disabled:g.length===0,children:[E.jsx(NS,{className:"h-4 w-4 ml-2"}),"إتمام البيع"]}),E.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[E.jsx(rt,{variant:"outline",size:"sm",children:"خصم"}),E.jsx(rt,{variant:"outline",size:"sm",onClick:R,disabled:g.length===0,children:"مسح الكل"})]})]})]})]})})]})})]})},Lm=({children:a})=>{const{isAuthenticated:i}=bu();return i?E.jsx(E.Fragment,{children:a}):E.jsx(Qi,{to:"/login"})},i2=({children:a})=>{const{isAuthenticated:i}=bu();return i?E.jsx(Qi,{to:"/dashboard"}):E.jsx(E.Fragment,{children:a})};function r2(){return E.jsx(Dv,{children:E.jsx("div",{className:"App",children:E.jsxs(cv,{children:[E.jsx(ea,{path:"/login",element:E.jsx(i2,{children:E.jsx(l2,{})})}),E.jsx(ea,{path:"/dashboard",element:E.jsx(Lm,{children:E.jsx(n2,{})})}),E.jsx(ea,{path:"/pos",element:E.jsx(Lm,{children:E.jsx(u2,{})})}),E.jsx(ea,{path:"/",element:E.jsx(Qi,{to:"/dashboard"})}),E.jsx(ea,{path:"*",element:E.jsx(Qi,{to:"/dashboard"})})]})})})}vg.createRoot(document.getElementById("root")).render(E.jsx(M.StrictMode,{children:E.jsx(r2,{})}));
