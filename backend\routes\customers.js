import express from 'express';
import { body } from 'express-validator';
import CustomerController from '../controllers/customerController.js';
import { authenticateToken, requireManager } from '../middleware/auth.js';

const router = express.Router();

// قواعد التحقق من البيانات
const customerValidation = [
    body('name')
        .notEmpty()
        .withMessage('اسم العميل مطلوب')
        .isLength({ min: 2, max: 100 })
        .withMessage('اسم العميل يجب أن يكون بين 2 و 100 حرف'),
    body('phone')
        .optional()
        .isMobilePhone('ar-SA')
        .withMessage('رقم الهاتف غير صحيح'),
    body('email')
        .optional()
        .isEmail()
        .withMessage('البريد الإلكتروني غير صحيح'),
    body('address')
        .optional()
        .isLength({ max: 200 })
        .withMessage('العنوان يجب أن يكون أقل من 200 حرف'),
    body('balance')
        .optional()
        .isFloat()
        .withMessage('الرصيد يجب أن يكون رقم')
];

const balanceUpdateValidation = [
    body('amount')
        .isFloat()
        .withMessage('المبلغ يجب أن يكون رقم'),
    body('operation')
        .optional()
        .isIn(['set', 'add', 'subtract'])
        .withMessage('العملية يجب أن تكون set أو add أو subtract')
];

// المسارات العامة (للقراءة)
router.get('/', authenticateToken, CustomerController.getAll);
router.get('/debtors', authenticateToken, CustomerController.getDebtors);
router.get('/phone/:phone', authenticateToken, CustomerController.getByPhone);
router.get('/:id', authenticateToken, CustomerController.getById);

// المسارات المحمية (للكتابة)
router.post('/', authenticateToken, customerValidation, CustomerController.create);
router.put('/:id', authenticateToken, customerValidation, CustomerController.update);
router.patch('/:id/balance', authenticateToken, balanceUpdateValidation, CustomerController.updateBalance);
router.delete('/:id', authenticateToken, requireManager, CustomerController.delete);

export default router;
