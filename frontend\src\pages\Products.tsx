import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { useAuthStore } from '../stores/authStore';
import { productsAPI } from '../services/api';
import { 
  Package, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  ArrowLeft,
  AlertTriangle,
  Eye
} from 'lucide-react';

interface Product {
  id: number;
  name: string;
  barcode?: string;
  selling_price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  unit: string;
  category_name?: string;
  is_active: boolean;
}

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showLowStock, setShowLowStock] = useState(false);

  const { user } = useAuthStore();
  const navigate = useNavigate();

  // تحميل المنتجات
  useEffect(() => {
    loadProducts();
  }, [showLowStock]);

  const loadProducts = async () => {
    try {
      setIsLoading(true);
      const filters: any = {};
      if (searchTerm) filters.search = searchTerm;
      if (showLowStock) filters.low_stock = true;

      const response = await productsAPI.getAll(filters);
      setProducts(response.products || []);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // البحث
  const handleSearch = () => {
    loadProducts();
  };

  // حذف منتج
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return;

    try {
      await productsAPI.delete(id);
      loadProducts();
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      alert('حدث خطأ أثناء حذف المنتج');
    }
  };

  // فلترة المنتجات حسب البحث
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode?.includes(searchTerm)
  );

  const canEdit = user?.role === 'admin' || user?.role === 'manager';

  return (
    <div className="min-h-screen bg-gray-50">
      {/* الهيدر */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                onClick={() => navigate('/dashboard')}
                className="ml-4"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Package className="h-6 w-6 text-primary ml-3" />
              <h1 className="text-xl font-bold text-gray-900">إدارة المنتجات</h1>
            </div>
            
            {canEdit && (
              <Button onClick={() => navigate('/products/add')}>
                <Plus className="h-4 w-4 ml-2" />
                إضافة منتج جديد
              </Button>
            )}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* أدوات البحث والفلترة */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="البحث في المنتجات (الاسم أو الباركود)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={isLoading}>
                  <Search className="h-4 w-4 ml-2" />
                  بحث
                </Button>
                <Button
                  variant={showLowStock ? "default" : "outline"}
                  onClick={() => setShowLowStock(!showLowStock)}
                >
                  <AlertTriangle className="h-4 w-4 ml-2" />
                  منخفض المخزون
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{products.length}</p>
                <p className="text-sm text-gray-600">إجمالي المنتجات</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {products.filter(p => p.stock_quantity > p.min_stock_level).length}
                </p>
                <p className="text-sm text-gray-600">متوفر</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">
                  {products.filter(p => p.stock_quantity <= p.min_stock_level).length}
                </p>
                <p className="text-sm text-gray-600">منخفض المخزون</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {products.reduce((sum, p) => sum + p.stock_quantity, 0)}
                </p>
                <p className="text-sm text-gray-600">إجمالي الكمية</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* جدول المنتجات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المنتجات</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p>جاري التحميل...</p>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>لا توجد منتجات</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3">اسم المنتج</th>
                      <th className="text-right p-3">الباركود</th>
                      <th className="text-right p-3">الفئة</th>
                      <th className="text-right p-3">سعر البيع</th>
                      <th className="text-right p-3">المخزون</th>
                      <th className="text-right p-3">الحالة</th>
                      <th className="text-right p-3">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredProducts.map((product) => (
                      <tr key={product.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-gray-500">{product.unit}</p>
                          </div>
                        </td>
                        <td className="p-3 text-sm text-gray-600">
                          {product.barcode || '-'}
                        </td>
                        <td className="p-3 text-sm">
                          {product.category_name || '-'}
                        </td>
                        <td className="p-3">
                          <span className="font-medium">{product.selling_price} ر.س</span>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center">
                            <span className={`font-medium ${
                              product.stock_quantity <= product.min_stock_level 
                                ? 'text-red-600' 
                                : 'text-green-600'
                            }`}>
                              {product.stock_quantity}
                            </span>
                            {product.stock_quantity <= product.min_stock_level && (
                              <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            product.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/products/${product.id}`)}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            {canEdit && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => navigate(`/products/${product.id}/edit`)}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => handleDelete(product.id)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Products;
