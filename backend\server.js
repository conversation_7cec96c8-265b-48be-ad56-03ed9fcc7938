import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// تحميل متغيرات البيئة
dotenv.config();

// إعداد المسارات
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// إنشاء تطبيق Express
const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الأمان
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// إعداد CORS
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));

// إعداد Rate Limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 دقيقة
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    message: {
        error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
    }
});
app.use('/api/', limiter);

// إعداد معالجة البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إعداد الملفات الثابتة
app.use('/uploads', express.static(join(__dirname, 'uploads')));

// استيراد قاعدة البيانات
import './config/database.js';

// استيراد المسارات
import authRoutes from './routes/auth.js';
import productRoutes from './routes/products.js';
import customerRoutes from './routes/customers.js';
// import saleRoutes from './routes/sales.js';

// المسارات الأساسية
app.get('/', (req, res) => {
    res.json({
        message: '🎉 مرحباً بك في SmartPOS API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

// مسار فحص الصحة
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        memory: process.memoryUsage()
    });
});

// تسجيل المسارات
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/customers', customerRoutes);
// app.use('/api/sales', saleRoutes);

// معالج الأخطاء العام
app.use((err, req, res, next) => {
    console.error('❌ خطأ في الخادم:', err.stack);
    res.status(500).json({
        error: 'حدث خطأ داخلي في الخادم',
        message: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// معالج المسارات غير الموجودة
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'المسار غير موجود',
        path: req.originalUrl
    });
});

// تشغيل الخادم
app.listen(PORT, () => {
    console.log(`
🚀 خادم SmartPOS يعمل على المنفذ ${PORT}
🌐 الرابط: http://localhost:${PORT}
📊 البيئة: ${process.env.NODE_ENV || 'development'}
⏰ الوقت: ${new Date().toLocaleString('ar-SA')}
    `);
});

export default app;
