import express from 'express';
import { body } from 'express-validator';
import ProductController from '../controllers/productController.js';
import { authenticateToken, requireManager } from '../middleware/auth.js';

const router = express.Router();

// قواعد التحقق من البيانات
const productValidation = [
    body('name')
        .notEmpty()
        .withMessage('اسم المنتج مطلوب')
        .isLength({ min: 2, max: 200 })
        .withMessage('اسم المنتج يجب أن يكون بين 2 و 200 حرف'),
    body('selling_price')
        .isFloat({ min: 0 })
        .withMessage('سعر البيع يجب أن يكون رقم موجب'),
    body('cost_price')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('سعر التكلفة يجب أن يكون رقم موجب'),
    body('stock_quantity')
        .optional()
        .isInt({ min: 0 })
        .withMessage('كمية المخزون يجب أن تكون رقم صحيح موجب'),
    body('min_stock_level')
        .optional()
        .isInt({ min: 0 })
        .withMessage('الحد الأدنى للمخزون يجب أن يكون رقم صحيح موجب'),
    body('barcode')
        .optional()
        .isLength({ min: 1, max: 50 })
        .withMessage('الباركود يجب أن يكون بين 1 و 50 حرف'),
    body('unit')
        .optional()
        .isLength({ min: 1, max: 20 })
        .withMessage('الوحدة يجب أن تكون بين 1 و 20 حرف'),
    body('category_id')
        .optional()
        .isInt({ min: 1 })
        .withMessage('معرف الفئة يجب أن يكون رقم صحيح موجب')
];

const stockUpdateValidation = [
    body('quantity')
        .isInt({ min: 0 })
        .withMessage('الكمية يجب أن تكون رقم صحيح موجب'),
    body('operation')
        .optional()
        .isIn(['set', 'add', 'subtract'])
        .withMessage('العملية يجب أن تكون set أو add أو subtract')
];

// المسارات العامة (للقراءة)
router.get('/', authenticateToken, ProductController.getAll);
router.get('/low-stock', authenticateToken, ProductController.getLowStock);
router.get('/barcode/:barcode', authenticateToken, ProductController.getByBarcode);
router.get('/:id', authenticateToken, ProductController.getById);

// المسارات المحمية (للكتابة - مديرين فقط)
router.post('/', authenticateToken, requireManager, productValidation, ProductController.create);
router.put('/:id', authenticateToken, requireManager, productValidation, ProductController.update);
router.patch('/:id/stock', authenticateToken, requireManager, stockUpdateValidation, ProductController.updateStock);
router.delete('/:id', authenticateToken, requireManager, ProductController.delete);

export default router;
