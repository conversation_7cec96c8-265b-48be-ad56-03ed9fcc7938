import Product from '../models/Product.js';
import { validationResult } from 'express-validator';

class ProductController {
    // الحصول على جميع المنتجات
    static async getAll(req, res) {
        try {
            const { category_id, search, low_stock } = req.query;
            
            const filters = {};
            if (category_id) filters.category_id = parseInt(category_id);
            if (search) filters.search = search;
            if (low_stock === 'true') filters.low_stock = true;

            const products = await Product.getAll(filters);
            
            res.json({
                success: true,
                products,
                count: products.length
            });

        } catch (error) {
            console.error('خطأ في الحصول على المنتجات:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على المنتجات'
            });
        }
    }

    // الحصول على منتج بالمعرف
    static async getById(req, res) {
        try {
            const { id } = req.params;
            const product = await Product.findById(parseInt(id));

            if (!product) {
                return res.status(404).json({
                    error: 'المنتج غير موجود'
                });
            }

            res.json({
                success: true,
                product
            });

        } catch (error) {
            console.error('خطأ في الحصول على المنتج:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على المنتج'
            });
        }
    }

    // البحث عن منتج بالباركود
    static async getByBarcode(req, res) {
        try {
            const { barcode } = req.params;
            const product = await Product.findByBarcode(barcode);

            if (!product) {
                return res.status(404).json({
                    error: 'المنتج غير موجود'
                });
            }

            res.json({
                success: true,
                product
            });

        } catch (error) {
            console.error('خطأ في البحث بالباركود:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء البحث عن المنتج'
            });
        }
    }

    // إنشاء منتج جديد
    static async create(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: 'بيانات غير صحيحة',
                    details: errors.array()
                });
            }

            const productData = req.body;
            const newProduct = await Product.create(productData);

            res.status(201).json({
                success: true,
                message: 'تم إنشاء المنتج بنجاح',
                product: newProduct
            });

        } catch (error) {
            console.error('خطأ في إنشاء المنتج:', error);
            
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                return res.status(409).json({
                    error: 'الباركود موجود مسبقاً'
                });
            }

            res.status(500).json({
                error: 'حدث خطأ أثناء إنشاء المنتج'
            });
        }
    }

    // تحديث منتج
    static async update(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: 'بيانات غير صحيحة',
                    details: errors.array()
                });
            }

            const { id } = req.params;
            const productData = req.body;

            const result = await Product.update(parseInt(id), productData);

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'المنتج غير موجود'
                });
            }

            res.json({
                success: true,
                message: 'تم تحديث المنتج بنجاح'
            });

        } catch (error) {
            console.error('خطأ في تحديث المنتج:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء تحديث المنتج'
            });
        }
    }

    // تحديث المخزون
    static async updateStock(req, res) {
        try {
            const { id } = req.params;
            const { quantity, operation = 'set' } = req.body;

            if (!quantity || quantity < 0) {
                return res.status(400).json({
                    error: 'الكمية يجب أن تكون رقم موجب'
                });
            }

            const result = await Product.updateStock(parseInt(id), quantity, operation);

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'المنتج غير موجود أو الكمية غير كافية'
                });
            }

            res.json({
                success: true,
                message: 'تم تحديث المخزون بنجاح'
            });

        } catch (error) {
            console.error('خطأ في تحديث المخزون:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء تحديث المخزون'
            });
        }
    }

    // الحصول على المنتجات منخفضة المخزون
    static async getLowStock(req, res) {
        try {
            const products = await Product.getLowStockProducts();
            
            res.json({
                success: true,
                products,
                count: products.length
            });

        } catch (error) {
            console.error('خطأ في الحصول على المنتجات منخفضة المخزون:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء الحصول على المنتجات'
            });
        }
    }

    // حذف منتج
    static async delete(req, res) {
        try {
            const { id } = req.params;
            const result = await Product.delete(parseInt(id));

            if (result.changes === 0) {
                return res.status(404).json({
                    error: 'المنتج غير موجود'
                });
            }

            res.json({
                success: true,
                message: 'تم حذف المنتج بنجاح'
            });

        } catch (error) {
            console.error('خطأ في حذف المنتج:', error);
            res.status(500).json({
                error: 'حدث خطأ أثناء حذف المنتج'
            });
        }
    }
}

export default ProductController;
